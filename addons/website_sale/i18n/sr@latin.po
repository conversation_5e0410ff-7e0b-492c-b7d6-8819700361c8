# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-24 08:59+0000\n"
"PO-Revision-Date: 2017-10-24 08:59+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "&amp;nbsp;"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "/shop/cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "/shop/payment"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"30-day money-back guarantee<br/>\n"
"                              Free Shipping in U.S.<br/>\n"
"                              Buy now, get in 2 days"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid "75 percent less reflection."
msgstr "75 percent less reflection."

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"margin:auto;width:100%;\">\n"
"                    <img src=\"/web/image/res.company/${user.company_id.id}/logo\" style=\"height: auto; width: 80px; margin-top:30px;\" alt=\"${user.company_id.name}\"/>\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    <p>Would you like to complete your purchase?</p><br/><br/>\n"
"                    % if object.order_line:\n"
"                    % for line in object.order_line:\n"
"                    <hr/>\n"
"                    <table width=\"100%\">\n"
"                        <tr>\n"
"                            <td style=\"padding: 10px; width:150px;\">\n"
"                                <img src=\"/web/image/product.product/${line.product_id.id}/image\" height=\"100px\" width=\"100px\"/>\n"
"                            </td>\n"
"                            <td>\n"
"                                <strong>${line.product_id.display_name}</strong><br/>${line.name}\n"
"                            </td>\n"
"                            <td width=\"100px\" align=\"right\">\n"
"                                ${(line.product_uom_qty) | int} ${(line.product_uom.name)}\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    % endfor\n"
"                    <hr/>\n"
"                    % endif\n"
"                    <br/><br/>\n"
"                    <center>\n"
"                        <a href=\"/shop/cart?access_token=${object.access_token}\" target=\"_blank\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">Resume order</a><br/><br/><br/>\n"
"                        <p><b>Thank you for shopping with ${user.company_id.name}!</b></p>\n"
"                    </center>\n"
"                </div>\n"
"            "
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid ""
"<br/>\n"
"                                <strong>Payment Status:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<br/>\n"
"                                e.g. for computers:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<i class=\"fa fa-arrow-right\" aria-hidden=\"true\"/> Add payment acquirers"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "<i class=\"fa fa-check\"/> Ship to this address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<i class=\"fa fa-envelope-o\"/> Ask Our Experts"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "<i class=\"fa fa-plus-square\" aria-hidden=\"true\"/> Add an address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print\"/> Print"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header
msgid ""
"<i class=\"fa fa-shopping-cart\"/>\n"
"                    My Cart"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/crm_team.py:58
#, python-format
msgid ""
"<p class=\"oe_view_nocontent_create\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"<small class=\"text-muted pull-right\">Source: https://termsfeed.com/blog"
"/sample-terms-and-conditions-template</small>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "<small><i class=\"fa fa-trash-o\"/> Remove</small>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<span class=\"col-xs-6 text-right h4 mt0\">Total:</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid ""
"<span class=\"col-xs-6 text-right text-muted\" title=\"Taxes may be updated "
"after providing shipping address\"> Taxes:</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<span class=\"col-xs-6 text-right text-muted\">Subtotal:</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"<span class=\"fa fa-chevron-left\" aria-hidden=\"true\"/>\n"
"                                            <span class=\"sr-only\">Previous</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"<span class=\"fa fa-chevron-right\" aria-hidden=\"true\"/>\n"
"                                            <span class=\"sr-only\">Next</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                                Everything is editable in your Odoo website. Feel free to edit any page title, field or button label. Simply hit *Edit* in upper-right corner to start editing the page."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                                Offering free delivery with a minimum amount or minimum number of items should drive up your average order value and help to compensate for the delivery costs."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                                You can create different rates based on order amount ranges (e.g. $10 up to a $50 order, then $5 beyond)."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.continue_shopping
msgid ""
"<span class=\"fa fa-long-arrow-left\"/>\n"
"                <span class=\"hidden-xs\">Continue Shopping</span>\n"
"                <span class=\"visible-xs-inline\">Continue</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<span class=\"fa fa-long-arrow-left\"/> Back"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"fa fa-long-arrow-left\"/> Previous"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "<span class=\"fa fa-long-arrow-left\"/> Return to Cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"hidden-xs\">Process Checkout</span>\n"
"                                        <span class=\"visible-xs-inline\">Checkout</span>\n"
"                                        <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import a file</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &gt;50 items</span>\n"
"                                    </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &lt;50 items</span>\n"
"                                    </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong> Cross-selling</strong>\n"
"                                    </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong> Rules &amp; Formulas</strong>\n"
"                                    </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Average baskets are &gt; 5 items?</strong><br/>\n"
"                                            <span class=\"small\"/>\n"
"                                    </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Several Prices</strong>\n"
"                                    </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Unique Price</strong>\n"
"                                    </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Upselling</strong>\n"
"                                    </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>You have a broad catalog?</strong><br/>\n"
"                                    </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                    <span class=\"fa fa-check-square-o\"/>\n"
"                                    <strong>Customize you product catalog</strong><br/>\n"
"                                        <span class=\"small\"/>\n"
"                                </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                <span class=\"fa fa-check-square-o\"/>\n"
"                                <strong>Customize the checkout process</strong><br/>\n"
"                                    <span class=\"small\"/>\n"
"                            </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-cc-paypal\"/><strong> Paypal</strong>\n"
"                                    <span class=\"small\">Recommended for Starters</span>\n"
"                                    </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-credit-card\"/><strong> "
"Authorize.net, Ingenico, etc.</strong></span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-lock\"/><strong>Wire "
"transfer</strong> (slow and inefficient)</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-shopping-cart\"/><strong> At cost price</strong> (customer pays what you pay)<br/>\n"
"                            <span class=\"small\">Advised if your packs vary in size/weight and/or if you ship worldwide</span>\n"
"                            </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-smile-o\"/><strong> Free delivery</strong><br/>\n"
"                            </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-table\"/><strong> Flat rates</strong> (everybody pays the same)<br/>\n"
"                            <span class=\"small\">Advised if you target a local audience</span>\n"
"                            </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "<span> &amp; Shipping</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "<span>Billing</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Confirmed</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<span>Next </span><span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "<span>Review Order</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid ""
"<span>Sort by</span>\n"
"                        <span class=\"caret\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "<strong>Add to Cart</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Bonuses:</strong> what you get on top of the offer"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Call-to-action</strong> short and clear: Add to Cart, Ask for quote,"
" etc."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Cons:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> customers have to check out to find out the delivery "
"price."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Cons:</strong> may be discouraging for your cheapest items"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> will require you to either absorb the cost or "
"slightly increase your prices to cover it."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Features and benefits:</strong> what the product does and why that "
"is good"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>For complex imports</strong> (&gt;5000 items, product variants, "
"pictures, etc.):"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>High-quality pictures</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Key features, emotional and commercial content</strong><br/>\n"
"                            Recommended for at least your top products, because it may have a big impact on your sales and conversion rates."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Mandatory content</strong><br/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Next actions:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Order Details:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "<strong>Payment Acquirer:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Payment Information:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Price</strong> with currency"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Product/Service name</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Pros:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pros:</strong> being transparent about your charges can help you "
"gain the trust of your customers."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pros:</strong> gives you a significant advantage over any "
"competitors that don't offer the same perk."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Pros:</strong> simple to understand"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Reassurance arguments</strong><br/>\n"
"                            Anticipate your customers questions &amp; worries on practical details like shipping rates &amp; policies, return &amp; replacement policies, payment acquirers &amp; security and your"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>See it in action in Odoo</strong><br/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Short description</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Technical information:</strong> what do you get and how does it "
"work?"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Tip for B2B users:</strong> you can create new manually-processed "
"payment acquirers (check, purchase order, etc.) by renaming 'Wire Transfer' "
"or duplicating it."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Total:</strong>"
msgstr "<strong>Ukupno:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Value proposition:</strong> what’s the end-benefit of this product "
"and who is it for?"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Variants</strong> like size or color"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"A <b>Governing Law</b> will inform users which laws govern the agreement. "
"This should the country in which your company is headquartered or the "
"country from which you operate your web site and mobile app."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"A <b>Limit What Users Can Do</b> clause can inform users that by agreeing to"
" use your service, they’re also agreeing to not do certain things. This can "
"be part of a very long and thorough list in your Terms and Conditions "
"agreements so as to encompass the most amount of negative uses."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"A <b>Links To Other Web Sites</b> clause will inform users that you are not "
"responsible for any third party web sites that you link to. This kind of "
"clause will generally inform users that they are responsible for reading and"
" agreeing (or disagreeing) with the Terms and Conditions or Privacy Policies"
" of these third parties."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"A <b>Termination</b> clause will inform that users’ accounts on your website"
" and mobile app or users’ access to your website and mobile (if users can’t "
"have an account with you) can be terminated in case of abuses or at your "
"sole discretion."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "A document to provide"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"A few % discount can bring you twice more customers! Here are a few kinds of"
" discounts to boost your sales:"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_12
msgid "A great Keyboard. Cordless."
msgstr "A great Keyboard. Cordless."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"A rounding per line is advised if your prices are tax-included. That way, "
"the sum of line subtotals equals the total with taxes."
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid "A screen worthy of iPad."
msgstr "A screen worthy of iPad."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:65
#, python-format
msgid "AT A GLANCE"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_is_abandoned_cart
msgid "Abandoned Cart"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/crm_team.py:45
#: model:ir.actions.act_window,name:website_sale.action_abandoned_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
#, python-format
msgid "Abandoned Carts"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:21
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_salesteams_view_kanban_inherit_website_sale
#, python-format
msgid "Abandoned Carts to Recover"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_cart_abandoned_delay
msgid "Abandoned Delay"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid ""
"About the size of a credit card — and just 5.4 mm thin — iPod nano is the thinnest iPod ever made.\n"
"                                    The 2.5-inch Multi-Touch display is nearly twice as big as the display on the previous iPod nano,\n"
"                                    so you can see more of the music, photos, and videos you love."
msgstr ""
"About the size of a credit card — and just 5.4 mm thin — iPod nano is the thinnest iPod ever made.\n"
"                                    The 2.5-inch Multi-Touch display is nearly twice as big as the display on the previous iPod nano,\n"
"                                    so you can see more of the music, photos, and videos you love."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template_accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before paying (cross-"
"sell strategy, e.g. for computers: mouse, keyboard, etc.). An algorithm "
"figures out a list of accessories based on all the products added to cart."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Accessories: screen, mouse"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_accessory_product_ids
msgid "Accessory Products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Add a step to request some extra customer data (picking preferences, etc.)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Add to Cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Add to Cart button"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Address"
msgstr "Adrese"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.option_collapse_products_categories
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid "All Products"
msgstr "Svi proizvodi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist_selectable
msgid "Allow the end user to choose this price list"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Allow to use on"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_alternative_product_ids
msgid "Alternative Products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.recommended_products
msgid "Alternative Products:"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team_abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid ""
"And at the Apple Online Store, you can configure your iMac with an even more"
" powerful Intel Core i7 processor, up to 3.5GHz."
msgstr ""
"And at the Apple Online Store, you can configure your iMac with an even more"
" powerful Intel Core i7 processor, up to 3.5GHz."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_2
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_3
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_5
msgid "And because it’s so easy to use, it’s easy to love."
msgstr "And because it’s so easy to use, it’s easy to love."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "Primjeni"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Apply multi-currencies"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Apply right VAT rates for digital products sold in EU"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Apply specific prices per country, discounts, etc."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignation"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignation of online orders"
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_catalog_variants_action
msgid "Attribute Values"
msgstr "Vrijednosti atributa"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Atributi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_automatic_invoice
msgid "Automatic Invoice"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:92
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:93
#, python-format
msgid "Average Order"
msgstr ""

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid "Beautiful 7.9‑inch display."
msgstr "Beautiful 7.9‑inch display."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid "Beautiful widescreen display."
msgstr "Beautiful widescreen display."

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:104
#, python-format
msgid "Best Sellers"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.bill_to
msgid "Bill To:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing Address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid "Brilliance onscreen. And behind it."
msgstr "Brilliance onscreen. And behind it."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid ""
"Buttons let you quickly play, pause, change songs, or adjust the volume.\n"
"                                    The smooth anodized aluminum design makes iPod nano feel as good as it sounds.\n"
"                                    And iPod nano wouldn’t be iPod nano without gorgeous, hard-to-choose-from color."
msgstr ""
"Buttons let you quickly play, pause, change songs, or adjust the volume.\n"
"                                    The smooth anodized aluminum design makes iPod nano feel as good as it sounds.\n"
"                                    And iPod nano wouldn’t be iPod nano without gorgeous, hard-to-choose-from color."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_can_directly_mark_as_paid
msgid "Can be directly marked as paid"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Can take up to several days to receive the money"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:12
#, python-format
msgid "Capture order payments when the delivery is completed."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_cart_quantity
msgid "Cart Quantity"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_cart_recovery_mail_template
msgid "Cart Recovery Email"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Cart are considered abandoned after"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:83
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:84
#, python-format
msgid "Carts"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Cash amount or percentage discounts"
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Catalog"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Catalog price: High to Low"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Catalog price: Low to High"
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template_public_categ_ids
msgid ""
"Categories can be published on the Shop page (online catalog grid) to help "
"customers find all the items within a category. To publish them, go to the "
"Shop page, hit Customize and turn *Product Categories* on. A product can "
"belong to several categories."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Change"
msgstr "Izmjeni"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order_can_directly_mark_as_paid
msgid ""
"Checked if the sales order can directly be marked as paid, i.e. if the quotation\n"
"                is sent or confirmed and if the payment acquire is of the type transfer or manual"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_child_id
msgid "Children Categories"
msgstr ""

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "Grad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:38
#, python-format
msgid "Click here to set an image describing your product."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Click here to unfold a Good Product Page"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:28
#, python-format
msgid "Click on <em>Continue</em> to create the product."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:60
#, python-format
msgid "Click on this button so your customers can see it."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Click to define a new category."
msgstr ""

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Color"
msgstr "Boja"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Community &amp; Enterprise users:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Company Details"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Company Name"
msgstr "Naziv Preduzeca"

#. module: website_sale
#: model:product.public.category,name:website_sale.Components
msgid "Components"
msgstr "Komponente"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.Computer_all_in_one
msgid "Computer all-in-one"
msgstr "Computer all-in-one"

#. module: website_sale
#: model:product.public.category,name:website_sale.sub_computers
msgid "Computers"
msgstr "Computers"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Configure your delivery methods (pricing, destinations)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Configure your pricelists"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Confirm <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm Order"
msgstr "Potvrdi narudžbu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm Order <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:5
#, python-format
msgid "Confirm orders when you get paid."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Confirmation Email"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Confirmation<span class=\"chevron\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed Orders"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:96
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:97
#, python-format
msgid "Conversion"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "Država"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country..."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Create your product items"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image_create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_style_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image_create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_create_date
#: model:ir.model.fields,field_description:website_sale.field_product_style_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid ""
"Creating such a stunningly thin design took some equally stunning feats of "
"technological innovation. We refined,re-imagined,or re-engineered everything"
" about iMac from the inside out. The result is an advanced, elegant all-in-"
"one computer that’s as much a work of art as it is state of the art."
msgstr ""
"Creating such a stunningly thin design took some equally stunning feats of "
"technological innovation. We refined,re-imagined,or re-engineered everything"
" about iMac from the inside out. The result is an advanced, elegant all-in-"
"one computer that’s as much a work of art as it is state of the art."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Currencies"
msgstr "Valute"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Customer Experience"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Customer Segments"
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
msgid "Customers"
msgstr "Kupci"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Customers review:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_currency_id
msgid "Default Currency"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_id
msgid "Default Pricelist"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "Podrazumjevani porez kod prodaje"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Default mode"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Default recovery email to send when a cart is abandoned"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Default sales tax applied to local transactions"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Delivery Strategy"
msgstr ""

#. module: website_sale
#: selection:res.config.settings,sale_delivery_settings:0
msgid ""
"Delivery methods are only used internally: the customer doesn't pay for "
"shipping costs"
msgstr ""

#. module: website_sale
#: selection:res.config.settings,sale_delivery_settings:0
msgid ""
"Delivery methods are selectable on the website: the customer pays for "
"shipping costs"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid "Design. The thinnest iPod ever."
msgstr "Design. The thinnest iPod ever."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template_website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.devices
msgid "Devices"
msgstr "Uredjaji"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Discount % or amounts, margins, roundings, periods, etc."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image_display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_display_name
#: model:ir.model.fields,field_description:website_sale.field_product_style_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display an images carousel on product pages"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display crossed-out public prices"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:50
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist_code
msgid "E-commerce Promotional Code"
msgstr ""

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Ease the product browsing with:"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:32
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Edit your payment policies and security to reassure your visitors"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Email"
msgstr "E-mail"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email Template"
msgstr "Šablon poruka"

#. module: website_sale
#: model:ir.model,name:website_sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email for Abandoned carts"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings_order_mail_template
msgid "Email sent to customer at the end of the checkout process"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email sent to the customer after the checkout"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:24
#, python-format
msgid "Enter a name for your new product"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/product.py:92
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast Colors are vivid and text is sharp on the iPad mini display.\n"
"                                    But what really makes it stand out is its size. At 7.9 inches,\n"
"                                    it’s perfectly sized to deliver an experience every bit as big as iPad."
msgstr ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast Colors are vivid and text is sharp on the iPad mini display.\n"
"                                    But what really makes it stand out is its size. At 7.9 inches,\n"
"                                    it’s perfectly sized to deliver an experience every bit as big as iPad."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_2
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_3
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_5
msgid ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast and fluid performance, FaceTime and\n"
"                                    iSight cameras, thousands of amazing apps, 10-hour\n"
"                                    battery life* — is everything you’ll love about\n"
"                                    iPad mini, too. And you can hold it in one hand."
msgstr ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast and fluid performance, FaceTime and\n"
"                                    iSight cameras, thousands of amazing apps, 10-hour\n"
"                                    battery life* — is everything you’ll love about\n"
"                                    iPad mini, too. And you can hold it in one hand."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid ""
"Everything you love about iPad — the beautiful screen,\n"
"                                        fast and fluid performance, FaceTime and iSight cameras, \n"
"                                        thousands of amazing apps, 10-hour battery life* — is everything\n"
"                                        you’ll love about iPad mini, too. And you can hold it in one hand."
msgstr ""
"Everything you love about iPad — the beautiful screen,\n"
"                                        fast and fluid performance, FaceTime and iSight cameras, \n"
"                                        thousands of amazing apps, 10-hour battery life* — is everything\n"
"                                        you’ll love about iPad mini, too. And you can hold it in one hand."

#. module: website_sale
#: model:product.public.category,name:website_sale.External_Hard_Drive
msgid "External Hard Drive"
msgstr "External Hard Drive"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
msgid "Extra Info<span class=\"chevron\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Extra Step"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid "Fast connections.The world over."
msgstr "Fast connections.The world over."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_model_fields
msgid "Fields"
msgstr "Polja"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_abandoned_orders_ecommerce
msgid ""
"Find here all the abandoned carts, i.e. the carts generated by your "
"website's visitors more than one hour ago and not confirmed."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Free and no setup"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Free delivery (see Delivery Strategy section)"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid "Friendly to the environment."
msgstr "Friendly to the environment."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Generate the invoice automatically when the order is confirmed"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid "Genius. Your own personal DJ."
msgstr "Genius. Your own personal DJ."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Give us your feedback..."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Daje redosled sekvenci pri prikazu liste kategorije proizvoda."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Go through the checkout process and make sure every single step is tailored "
"to your activity:"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_style_html_class
msgid "HTML Classes"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute_value_html_color
msgid "HTML Color Index"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP routing"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid "Have Genius call the tunes."
msgstr "Have Genius call the tunes."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Have a promo code? Fill in this field and apply."
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.Headset
msgid "Headset"
msgstr "Headset"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Here are <strong>some pros and cons</strong> to help you decide:<br/>"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_attribute_value_html_color
msgid ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color on the website if the attibute type is 'Color'."
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid "Highly rated designs."
msgstr "Highly rated designs."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid ""
"How did we make an already gorgeous widescreen display even better? By "
"making it 75 percent less reflective. And by re-architecting the LCD and "
"moving it right up against the cover glass. So you see your photos, games, "
"movies, and everything else in vivid, lifelike detail."
msgstr ""
"How did we make an already gorgeous widescreen display even better? By "
"making it 75 percent less reflective. And by re-architecting the LCD and "
"moving it right up against the cover glass. So you see your photos, games, "
"movies, and everything else in vivid, lifelike detail."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid "How to get your groove on."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "How total tax amount is computed in orders and invoices"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "I agree to the"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_id
#: model:ir.model.fields,field_description:website_sale.field_product_style_id
msgid "ID"
msgstr "ID"

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid "If it's made for iPad, it's made for iPad mini."
msgstr "If it's made for iPad, it's made for iPad mini."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"If you deal with a major shipper (UPS, FedEx, DHL, etc.), activate the "
"related connector in Odoo to compute accurate costs and print labels."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"If you're selling digital goods to customers in the EU, you must charge VAT "
"based on your customers' locations. This rule applies regardless of you are "
"located. Digital goods are defined in the legislation as broadcasting, "
"telecommunications, and services that are electronically supplied instead of"
" shipped. Gift cards sent online are not included in the definition."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"If your website or mobile apps allows users to create content and make that "
"content public to other users, a <b>Content</b> section will inform users "
"that they own the rights to the content they have created.<br/>The “Content”"
" clause usually mentions that users must give you (the website or mobile app"
" developer) a license so that you can share this content on your "
"website/mobile app and to make it available to other users.<br/>Because the "
"content created by users is public to other users, a DMCA notice clause (or "
"Copyright Infringement ) section is helpful to inform users and copyright "
"authors that, if any content is found to be a copyright infringement, you "
"will respond to any DMCA take down notices received and you will take down "
"the content."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image_image
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image
msgid "Image"
msgstr "Slika"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_product_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_product_image_ids
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Images"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Imagine a new customer adds a product to cart and leaves your website "
"because of shipping costs... Defining a good delivery strategy is difficult."
" You don't want to cut into your margins, but you want to remain price-"
"competitive."
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid "Individually calibrated for true-to-life color."
msgstr "Individually calibrated for true-to-life color."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Install Optional Products (cross-selling)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Install shipping connectors"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings_module_website_sale_stock
msgid "Installs *e-Commerce Inventory*"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:502
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_module_website_sale_stock
msgid "Inventory"
msgstr "Skladište"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_invoices_ecommerce
msgid "Invoices"
msgstr "Fakture"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_module_account_invoicing
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "Fakturisanje"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:146
#, python-format
msgid "It is forbidden to modify a sales order which is not in draft status"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"It's difficult to recommend one over the others. So, simply pick the one "
"that is more popular in your country!"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid "Key Features"
msgstr "Key Features"

#. module: website_sale
#: model:product.public.category,name:website_sale.Keyboard_Mouse
msgid "Keyboard / Mouse"
msgstr "Keyboard / Mouse"

#. module: website_sale
#: model:product.public.category,name:website_sale.laptops
msgid "Laptops"
msgstr "Laptops"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image___last_update
#: model:ir.model.fields,field_description:website_sale.field_product_public_category___last_update
#: model:ir.model.fields,field_description:website_sale.field_product_style___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Last Month"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner_last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users_last_website_so_id
msgid "Last Online Sales Order"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image_write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_style_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image_write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_write_date
#: model:ir.model.fields,field_description:website_sale.field_product_style_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Last Week"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Last Year"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Legal Name"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let returning shoppers save products in a whishlist"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a shipping address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Let them buy straight from catalog with:"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:15
#, python-format
msgid "Let's create your first product."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:64
#, python-format
msgid ""
"Let's now take a look at your administration dashboard to get your eCommerce"
" website ready in no time."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "List displaying"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Log In"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Logo"
msgstr "Logo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Main Currency"
msgstr "Glavna valuta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Make sure your customers can go through the shopping process in a very short"
" time, otherwise they will leave your website. Here are some actions to "
"tailor the process to your business."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage availability of products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form
msgid "Mark as Paid"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image_medium
msgid "Medium-sized image"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image_medium
msgid ""
"Medium-sized image of the category. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Min. qty or period"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid "More energy efficient."
msgstr "More energy efficient."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_group_website_multiimage
msgid "Multi-Images"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Multi-buy offers (2 products sold together for less than their individual "
"price)"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid "Music. It's what beats inside."
msgstr "Music. It's what beats inside."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.js:15
#, python-format
msgid "My Cart"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_name
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Name"
msgstr "Naziv"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Name - A to Z"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Name - Z to A"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.network
msgid "Network"
msgstr "Network"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/controllers/main.py:967
#: code:addons/website_sale/static/src/js/website_sale.editor.js:32
#: model_terms:ir.ui.view,arch_db:website_sale.content_new_product
#, python-format
msgid "New Product"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Next <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "No monthly fees for standard offer"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined."
msgstr ""

#. module: website_sale
#: selection:res.config.settings,sale_delivery_settings:0
msgid "No shipping management on website"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "None website"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Now to Follow Your Order"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team_abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale.xml:12
#, python-format
msgid "OK"
msgstr "OK"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Once activated, discounted prices can be applied from Pricelists setup form "
"(Discount policy field)."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:55
#, python-format
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/crm_team.py:39
#, python-format
msgid "Online Sales"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Online users: we do it for you!"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_only_services
msgid "Only Services"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:68
#, python-format
msgid "Open your website app here."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_module_website_sale_options
msgid "Optional Products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Options: warranty, software"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_order_mail_template
msgid "Order Confirmation Email"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_website_order_line
msgid "Order Lines displayed on Website"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order_website_order_line
msgid ""
"Order Lines to be displayed on the website. They should not be used for "
"computation purpose."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:67
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:68
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#, python-format
msgid "Orders"
msgstr "Narudžbe"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:88
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:89
#, python-format
msgid "Orders/Day"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid "Over 375,000 apps."
msgstr "Over 375,000 apps."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_parent_id
msgid "Parent Category"
msgstr "Roditeljska kategorija"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:755
#, python-format
msgid "Pay Now"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay Now <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay with"
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_acquirers
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Payment Acquirers"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "Payment Information"
msgstr "Informacije Placanja"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Payment must be reconciled manually"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Payment<span class=\"chevron\"/>"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.payment_transaction_action_payments_to_capture
msgid "Payments To Capture"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:14
#, python-format
msgid "Payments to Capture"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.Pen_Drive
msgid "Pen Drive"
msgstr "Pen Drive"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "Telefon:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Pictures gallery:"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid "Playlists. The perfect mix for every mood."
msgstr "Playlists. The perfect mix for every mood."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid ""
"Powered by fourth-generation Intel Core processors, this iMac is the fastest"
" yet. Every model in the lineup comes standard with a quad-core Intel Core "
"i5 processor, starting at 2.7GHz and topping out at 3.4GHz."
msgstr ""
"Powered by fourth-generation Intel Core processors, this iMac is the fastest"
" yet. Every model in the lineup comes standard with a quad-core Intel Core "
"i5 processor, starting at 2.7GHz and topping out at 3.4GHz."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Prefered by some customers"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:109
#, python-format
msgid "Previous Month"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:107
#, python-format
msgid "Previous Week"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:111
#, python-format
msgid "Previous Year"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Price"
msgstr "Cijena"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "Cjenovnik"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_product_pricelist3
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Cjenovnici"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Odredjivanje cijene"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Pricing &amp; Discounts"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.printer
msgid "Printer"
msgstr "Printer"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:107
#: model:ir.model,name:website_sale.model_product_product
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
#, python-format
msgid "Product"
msgstr "Proizvod"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_module_website_sale_comparison
msgid "Product Comparison Tool"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Product Name"
msgstr "Naziv proizvoda"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Product Pages"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_variants
msgid "Product Variants"
msgstr "Varijante proizvoda"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Product attributes filter"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Product categories list"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Product descriptions"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_price
msgid "Product not available"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Product not found!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product prices displaying in web catalog"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_settings
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Products"
msgstr "Proizvodi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Promo Code"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Promote"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Provide customers with product-specific links or downloadable content in the"
" confirmation page of the checkout process if the payment gets through. To "
"do so, attach some files to a product using the new Files button and publish"
" them."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
msgid "Published"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push down"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push to bottom"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push to top"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push up"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Put the practical details (shipping, payment acquirers, etc.) as links in the footer; That way, they will be accessible \n"
"                            all your product pages."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Qty:"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:108
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
#, python-format
msgid "Quantity"
msgstr "Količina"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Quantity-based discounts (B2B)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Quick and easy to set up"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Quotation"
msgstr "Ponuda"

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Radio"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_rating_ids
msgid "Rating"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_rating_last_feedback
msgid "Rating Last Feedback"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_rating_last_image
msgid "Rating Last Image"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_rating_last_value
msgid "Rating Last Value"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_rating_count
msgid "Rating count"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_rating_last_feedback
msgid "Reason of the rating"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Recovery Email to Send"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image_product_tmpl_id
msgid "Related Product"
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_reporting
msgid "Reporting"
msgstr "Izvještavanje"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Return to the product list."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Review your catalog"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid ""
"Right from the start, apps made for iPad also work with iPad mini.\n"
"                                    They’re immersive, full-screen apps that let you do almost anything\n"
"                                    you can imagine. And with automatic updates,\n"
"                                    you're always getting the best experience possible."
msgstr ""
"Right from the start, apps made for iPad also work with iPad mini.\n"
"                                    They’re immersive, full-screen apps that let you do almost anything\n"
"                                    you can imagine. And with automatic updates,\n"
"                                    you're always getting the best experience possible."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_2
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_3
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_5
msgid ""
"Right from the start, there’s a lot to love about\n"
"                                    iPad. It’s simple yet powerful. Thin and light yet\n"
"                                    full-featured. It can do just about everything and\n"
"                                    be just about anything."
msgstr ""
"Right from the start, there’s a lot to love about\n"
"                                    iPad. It’s simple yet powerful. Thin and light yet\n"
"                                    full-featured. It can do just about everything and\n"
"                                    be just about anything."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid ""
"Right from the start, there’s a lot to love about iPad.\n"
"                                   It’s simple yet powerful. Thin and light yet full-\n"
"                                   featured. It can do just about everything and be just\n"
"                                   about anything.And because it’s so easy to use, it’s\n"
"                                   easy to love."
msgstr ""
"Right from the start, there’s a lot to love about iPad.\n"
"                                   It’s simple yet powerful. Thin and light yet full-\n"
"                                   featured. It can do just about everything and be just\n"
"                                   about anything.And because it’s so easy to use, it’s\n"
"                                   easy to love."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Rounding Method"
msgstr "Način zaokruživanja"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Sale"
msgstr "Prodaja"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Report"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:37
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:49
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
#: model:ir.ui.menu,name:website_sale.menu_report_sales
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#, python-format
msgid "Sales"
msgstr "Prodaja"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website_salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Channel"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Report"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:35
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:47
#, python-format
msgid "Sales Since Last"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website_salesperson_id
msgid "Salesperson"
msgstr "Prodavač"

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid ""
"Say you’re listening to a song you love and you want to stay in the mood.\n"
"                                        Just tap Genius. It finds other songs on iPod nano that go great together\n"
"                                        and makes a Genius playlist for you. For more song combinations\n"
"                                        you wouldn’t have thought of yourself, create Genius Mixes in iTunes\n"
"                                        and sync the ones you like to iPod nano. Then tap Genius Mixes and\n"
"                                        rediscover songs you haven’t heard in a while — or find music you forgot you even had."
msgstr ""
"Say you’re listening to a song you love and you want to stay in the mood.\n"
"                                        Just tap Genius. It finds other songs on iPod nano that go great together\n"
"                                        and makes a Genius playlist for you. For more song combinations\n"
"                                        you wouldn’t have thought of yourself, create Genius Mixes in iTunes\n"
"                                        and sync the ones you like to iPod nano. Then tap Genius Mixes and\n"
"                                        rediscover songs you haven’t heard in a while — or find music you forgot you even had."

#. module: website_sale
#: model:product.public.category,name:website_sale.Screen
msgid "Screen"
msgstr "Screen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "See how to configure delivery methods and shipping connectors"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "See how to configure payment acquirers"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "See how to configure pricing &amp; discounts"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "See how to customize your catalog page"
msgstr ""

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Select"
msgstr "Izaberi"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:20
#, python-format
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Select optional products from the configuration form of your main products "
"(in Sales tab)."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Select this address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Select your pricing preference and allow discounts"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Select your shipping management policy"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist_selectable
msgid "Selectable"
msgstr "Selektivan"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sell"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sell More"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell content to download or URL links"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell in several currencies"
msgstr ""

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_actions_server_sale_cart_recovery_email
msgid "Send a Cart Recovery Email"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send a recovery email to authentified shoppers"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:19
#, python-format
msgid "Send a recovery email to visitors who haven't completed their order."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: website_sale
#: model:product.public.category,name:website_sale.server
msgid "Server"
msgstr "Server"

#. module: website_sale
#: model:product.public.category,name:website_sale.services
msgid "Services"
msgstr "Usluge"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Set alternatives, accessories and options in product detail form (in Sales "
"tab)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to sell variants"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.bill_to
msgid "Ship To:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_group_delivery_invoice_address
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping Address"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_module_website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_sale_delivery_settings
msgid "Shipping Management"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model:website.menu,name:website_sale.menu_shop
msgid "Shop"
msgstr "Prodavnica"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Acquirer"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Shopping Cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Shopping Experience"
msgstr ""

#. module: website_sale
#: model:res.groups,name:website_sale.group_website_multi_image
msgid "Show multi image on eCommerce"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Size"
msgstr "Veličina"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_size_x
msgid "Size X"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_size_y
msgid "Size Y"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image_small
msgid "Small-sized image"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image_small
msgid ""
"Small-sized image of the category. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.Software
msgid "Software"
msgstr "Software"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:76
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:109
#, python-format
msgid "Sold"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Some acquirers like Authorize.net and Ingenico can be used as payment "
"gateways with your own payment processor."
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:520
#, python-format
msgid "Some required fields are empty."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Sorry, this product is not available anymore."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sort by price"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Sorting by :"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.Speakers
msgid "Speakers"
msgstr "Speakers"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Start your online shop by creating 3 products pages.<br/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province..."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street 2"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street <span class=\"hidden-xs\"> and Number</span>"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_style_name
msgid "Style Name"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_style_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_style_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Styles"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Subtotal"
msgstr "Međuzbir"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Subtotal:"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template_alternative_product_ids
msgid ""
"Suggest more expensive alternatives to your customers (upsell strategy). "
"Those products show up on the product page."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Suggest optional products when adding to cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Suggest options and accessories"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Suggest pricier alternatives"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested Accessories:"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid "Sync to your heart’s content."
msgstr "Sync to your heart’s content."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "TIN / VAT"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid ""
"Tap to play your favorite songs. Or entire albums.\n"
"                                    Or everything by one artist. You can even browse by genres or composers.\n"
"                                    Flip through your music: Album art looks great on the bigger screen.\n"
"                                    Or to keep things fresh, give iPod nano a shake and it shuffles to a different song in your music library."
msgstr ""
"Tap to play your favorite songs. Or entire albums.\n"
"                                    Or everything by one artist. You can even browse by genres or composers.\n"
"                                    Flip through your music: Album art looks great on the bigger screen.\n"
"                                    Or to keep things fresh, give iPod nano a shake and it shuffles to a different song in your music library."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Tax ID"
msgstr "ID takse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Taxes"
msgstr "Porezi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Taxes:"
msgstr "Porezi:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Terms &amp; Conditions"
msgstr "Dogovori i uslovi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid "Terms &amp;amp; Conditions"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"The <b>Intellectual Property</b> disclosure will inform users that the "
"contents, logo and other visual media you created is your property and is "
"protected by copyright laws."
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid "The desktop. In its most advanced form ever"
msgstr "The desktop. In its most advanced form ever"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"The first step is to set up your company's information. This is used in "
"several places: Contact us page, website header &amp; footer, printed "
"business documents like orders and invoices, etc."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_2
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_3
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_5
msgid "The full iPad experience."
msgstr "The full iPad experience."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_12
msgid ""
"The incredibly thin Apple Wireless Keyboard uses Bluetooth technology,\n"
"                                    which makes it compatible with iPad. And you’re free to type wherever\n"
"                                    you like — with the keyboard in front of your iPad or on your lap."
msgstr ""
"The incredibly thin Apple Wireless Keyboard uses Bluetooth technology,\n"
"                                    which makes it compatible with iPad. And you’re free to type wherever\n"
"                                    you like — with the keyboard in front of your iPad or on your lap."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:247
#, python-format
msgid ""
"The quote should be sent and the payment acquirer type should be manual or "
"wire transfer"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "There are two strategies to sell more to a customer:"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_2
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_3
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_5
msgid "There is less of it, but no less to it."
msgstr "There is less of it, but no less to it."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:53
#, python-format
msgid "There is no recent confirmed order."
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid "There's less of it, but no less to it."
msgstr "There's less of it, but no less to it."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid ""
"There’s another way to get a good mix of music on iPod: Let Genius do the work.\n"
"                                        Activate Genius in iTunes on your computer, and it automatically finds songs that sound\n"
"                                        great together. Then it creates Genius Mixes, which you can easily sync to your iPod.\n"
"                                        It’s the perfect way to rediscover songs you haven’t listened to in forever."
msgstr ""
"There’s another way to get a good mix of music on iPod: Let Genius do the work.\n"
"                                        Activate Genius in iTunes on your computer, and it automatically finds songs that sound\n"
"                                        great together. Then it creates Genius Mixes, which you can easily sync to your iPod.\n"
"                                        It’s the perfect way to rediscover songs you haven’t listened to in forever."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "This adds the choice of a currency on pricelists."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image
msgid ""
"This field holds the image used as image for the category, limited to "
"1024x1024px."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"To collect payments, you can either request your customers to process it manually (wire transfer, etc.) or redirect them to payment acquirers.<br/>\n"
"                        Using payment acquirers costs you money, so take some time to compare them."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"To do so, take the most out of Odoo's <strong>pricing options</strong>:<br/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "Total"
msgstr "Ukupno"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute_type
msgid "Type"
msgstr "Tip"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid "Ultrafast wireless."
msgstr "Ultrafast wireless."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_14
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_4
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_6
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_7
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_8
msgid "Ultrathin design"
msgstr "Ultrathin design"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Unit Price"
msgstr "Jed. cena"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:7
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
#, python-format
msgid "Unpaid Orders"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:75
#, python-format
msgid "Untaxed Total Sold"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:42
#, python-format
msgid "Upload an image from your local library."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Use our import templates to get your catalog ready in no time!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "View Cart ("
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_published
msgid "Visible in Website"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Website"
msgstr "Internet stranica"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product_public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_public_categ_ids
msgid "Website Product Category"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_sequence
msgid "Website Sequence"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_url
msgid "Website URL"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_description
msgid "Website meta description"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_title
msgid "Website meta title"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_price
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_price
msgid "Website price"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_price_difference
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_price_difference
msgid "Website price difference"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_public_price
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_public_price
msgid "Website public price"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team_website_ids
msgid "Websites"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_crm_team_website_ids
msgid "Websites using this sales channel"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid "When one playlist isn’t enough."
msgstr "When one playlist isn’t enough."

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_2
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_3
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_5
msgid "Why you'll love an iPad."
msgstr "Why you'll love an iPad."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings_module_website_sale_wishlist
msgid "Wishlists"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_1
msgid ""
"With advanced Wi‑Fi that’s up to twice as fast as\n"
"                                   any previous-generation iPad and access to fast\n"
"                                   cellular data networks around the world, iPad mini\n"
"                                   lets you download content, stream video,\n"
"                                   and browse the web at amazing speeds."
msgstr ""
"With advanced Wi‑Fi that’s up to twice as fast as\n"
"                                   any previous-generation iPad and access to fast\n"
"                                   cellular data networks around the world, iPad mini\n"
"                                   lets you download content, stream video,\n"
"                                   and browse the web at amazing speeds."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"With the first mode you can set several prices in the product config form "
"(from Sales tab). With the second one, you set prices and computation rules "
"from Pricelists."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "You can setup 3 types of <strong>payment acquirers in Odoo:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "You don't have any confirmed order from the website."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.payment_transaction_action_payments_to_capture
msgid "You don't have any payments to capture from the website."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
msgid "You don't have any unpaid order from the website."
msgstr ""

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart."
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid ""
"You probably have multiple playlists in iTunes on your computer.\n"
"                                        One for your commute. One for the gym. Sync those playlists\n"
"                                        to iPod, and you can play the perfect mix for whatever\n"
"                                        mood strikes you. VoiceOver tells you the name of each playlist,\n"
"                                        so it’s easy to switch between them and find the one you want without looking."
msgstr ""
"You probably have multiple playlists in iTunes on your computer.\n"
"                                        One for your commute. One for the gym. Sync those playlists\n"
"                                        to iPod, and you can play the perfect mix for whatever\n"
"                                        mood strikes you. VoiceOver tells you the name of each playlist,\n"
"                                        so it’s easy to switch between them and find the one you want without looking."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_abandoned_orders_ecommerce
msgid "You should send an email to revive them!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Your Address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Your Address\n"
"                                        <small> or </small>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Your Reference"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Your cart is empty!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "all angles, detailed view, package, etc."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "code..."
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
msgid "eCommerce"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Categories"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.search count
msgid "found)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "hours"
msgstr ""

#. module: website_sale
#: model:sale.order.line,website_description:website_sale.website_sale_order_line_11
msgid ""
"iTunes on your Mac or PC makes it easy to load up\n"
"                                        your iPod. Just choose the playlists, audiobooks,\n"
"                                        podcasts, and other audio files you want, then sync."
msgstr ""
"iTunes on your Mac or PC makes it easy to load up\n"
"                                        your iPod. Just choose the playlists, audiobooks,\n"
"                                        podcasts, and other audio files you want, then sync."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "items)"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings_cart_abandoned_delay
msgid "number of hours after which the cart is considered abandoned"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "product's availability (In Stock, Not Available, etc.)"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute_value
msgid "product.attribute.value"
msgstr "product.attribute.value"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "product.image"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_style
msgid "product.style"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "set company logo"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "terms &amp; conditions"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "use Odoo API to import through scripts"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist_website_id
msgid "website"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "what do the customers think of the product"
msgstr ""
