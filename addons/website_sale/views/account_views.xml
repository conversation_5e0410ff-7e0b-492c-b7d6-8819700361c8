<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="action_invoices_ecommerce" model="ir.actions.act_window">
        <field name="name">Invoices</field>
        <field name="res_model">account.move</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('team_id.website_ids', '!=', False)]</field>
        <field name="view_id" ref="account.view_invoice_tree"/>
        <field name="context">{'move_type':'out_invoice'}</field>
        <field name="search_view_id" ref="account.view_account_invoice_filter"/>
    </record>

    <record id="website_product_pricelist3" model="ir.actions.act_window">
        <field name="name">Pricelists</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.pricelist</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('website_id', '!=', False)]</field>
        <field name="search_view_id" ref="product.product_pricelist_view_search" />
        <field name="context">{"default_base":'list_price'}</field>
    </record>

    <record id="account_move_view_form" model="ir.ui.view">
        <field name="name">account.move.form.inherit.website_sale</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='sale_info_group']" position="inside">
                <field name="website_id" groups="website.group_multi_website" attrs="{'invisible': [('website_id', '=', False)]}"/>
            </xpath>
        </field>
    </record>
</odoo>
