<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--product history-->
    <record id="website_sale_visitor_page_view_tree" model="ir.ui.view">
        <field name="name">website.track.view.tree</field>
        <field name="model">website.track</field>
        <field name="arch" type="xml">
            <tree string="Visitor Product Views History" create="0">
                <field name="visitor_id"/>
                <field name="product_id"/>
                <field name="visit_datetime"/>
            </tree>
        </field>
    </record>

    <record id="website_sale_visitor_page_view_graph" model="ir.ui.view">
        <field name="name">website.track.view.graph</field>
        <field name="model">website.track</field>
        <field name="arch" type="xml">
            <graph string="Visitor Product Views" sample="1">
                <field name="product_id"/>
            </graph>
        </field>
    </record>

    <record id="website_sale_visitor_product_action" model="ir.actions.act_window">
        <field name="name">Product Views History</field>
        <field name="res_model">website.track</field>
        <field name="view_mode">tree</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('website_sale_visitor_page_view_tree')}),
            (0, 0, {'view_mode': 'graph', 'view_id': ref('website_sale_visitor_page_view_graph')}),
        ]"/>
        <field name="domain">[('visitor_id', '=', active_id), ('product_id', '!=', False)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
              No product views yet for this visitor
            </p>
        </field>
    </record>

    <record id="website_sale_visitor_page_view_search" model="ir.ui.view">
        <field name="name">website.track.view.search</field>
        <field name="model">website.track</field>
        <field name="inherit_id" ref="website.website_visitor_page_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='url']" position="after">
                <field name="product_id"/>
            </xpath>
            <xpath expr="//filter[@name='type_url']" position="after">
                <filter string="Products" name="type_product" domain="[('product_id', '!=', False)]"/>
            </xpath>
            <xpath expr="//filter[@name='group_by_url']" position="after">
                <filter string="Product" name="group_by_product" domain="[]" context="{'group_by': 'product_id'}"/>
            </xpath>
        </field>
    </record>

    <!-- website visitor views -->
    <record id="website_sale_visitor_view_form" model="ir.ui.view">
        <field name="name">website.visitor.view.form</field>
        <field name="model">website.visitor</field>
        <field name="inherit_id" ref="website.website_visitor_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='%(website.website_visitor_page_action)d']" position="after">
                <button name="%(website_sale.website_sale_visitor_product_action)d" type="action"
                    class="oe_stat_button"
                    icon="fa-tags">
                    <field name="visitor_product_count" widget="statinfo" string="Product Views"/>
                </button>
            </xpath>
            <xpath expr="//group[@id='visits']/field[@name='page_ids']" position="after">
                <field name="product_ids" string="Products" widget="many2many_tags"/>
            </xpath>
        </field>
    </record>

    <record id="website_sale_visitor_view_tree" model="ir.ui.view">
        <field name="name">website.visitor.view.tree</field>
        <field name="model">website.visitor</field>
        <field name="inherit_id" ref="website.website_visitor_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='page_ids']" position="after">
                <field name="product_ids" widget="many2many_tags" string="Products"/>
            </xpath>
        </field>
    </record>

    <record id="website_sale_visitor_view_kanban" model="ir.ui.view">
        <field name="name">website.visitor.view.kanban</field>
        <field name="model">website.visitor</field>
        <field name="inherit_id" ref="website.website_visitor_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='page_ids']" position="after">
                <field name="product_ids"/>
            </xpath>
            <xpath expr="//div[@id='o_page_count']" position="after">
                 <div id="o_product_count">Visited Products<span class="float-right font-weight-bold"><field name="product_count"/></span></div>
            </xpath>
        </field>
    </record>

    <!-- website track views -->
    <record id="website_sale_visitor_track_view_tree" model="ir.ui.view">
        <field name="name">website.track.view.tree</field>
        <field name="model">website.track</field>
        <field name="inherit_id" ref="website.website_visitor_track_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='url']" position="after">
                <field name="product_id"/>
            </xpath>
        </field>
    </record>

    <record id="website_sale_visitor_track_view_graph" model="ir.ui.view">
        <field name="name">website.track.view.graph</field>
        <field name="model">website.track</field>
        <field name="inherit_id" ref="website.website_visitor_track_view_graph"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='url']" position="after">
                <field name="product_id"/>
            </xpath>
        </field>
    </record>
</odoo>
