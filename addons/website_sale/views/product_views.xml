<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="product_template_search_view_website" model="ir.ui.view">
        <field name="name">product.template.search.published</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='consumable']" position="after">
                <separator/>
                <filter string="Published" name="published" domain="[('is_published', '=', True)]"/>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="product_product_website_tree_view">
        <field name="name">product.product.website.tree</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_product_tree_view"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="website_id" groups="website.group_multi_website" optional="show"/>
                <field name="is_published" string="Is Published" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- We want website_id to be shown outside of website module like other models -->
    <record model="ir.ui.view" id="product_template_view_tree">
        <field name="name">product.template.view.tree.inherit.website_sale</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view"/>
        <field name="arch" type="xml">
            <field name="default_code" position="after">
                <field name="website_id" groups="website.group_multi_website" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- only website module template view should use the website_sequence -->
    <record model="ir.ui.view" id="product_template_view_tree_website_sale">
        <field name="name">product.template.view.tree.website_sale</field>
        <field name="mode">primary</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="website_sale.product_template_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="/tree" position="attributes">
              <attribute name="default_order">website_sequence</attribute>
            </xpath>
            <field name="priority" position="before">
                <field name="website_sequence" widget="handle"/>
            </field>
            <field name="website_id" position="after">
                <field name="public_categ_ids" widget="many2many_tags" string="Categories" optional="show"/>
                <field name="is_published" string="Is Published" optional="hide"/>
            </field>
        </field>
    </record>

    <record model="ir.ui.view" id="product_template_view_kanban_website_sale">
        <field name="name">product.template.view.kanban.website_sale</field>
        <field name="mode">primary</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_kanban_view"/>
        <field name="arch" type="xml">
            <xpath expr="/kanban" position="attributes">
              <attribute name="default_order">website_sequence</attribute>
            </xpath>
            <field name="id" position="after">
                <field name="website_sequence"/>
            </field>
        </field>
    </record>

    <record id="product_template_action_website" model="ir.actions.act_window">
        <field name="name">Products</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">tree,kanban,form,activity</field>
        <field name="view_id"/>
        <field name="search_view_id" ref="product_template_search_view_website"/>
        <field name="context">{'search_default_published': 1, 'tree_view_ref':'website_sale.product_template_view_tree_website_sale', 'kanban_view_ref':'website_sale.product_template_view_kanban_website_sale'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new product
            </p><p>
                A product can be either a physical product or a service that you sell to your customers.
            </p>
        </field>
    </record>

    <record model="ir.ui.view" id="product_template_only_website_form_view">
        <field name="name">product.template.product.only.website.form</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <label for="base_unit_count" groups="website_sale.group_show_uom_price"/>
                <div name="base_unit_price" groups="website_sale.group_show_uom_price" class="o_row">
                    <field name="base_unit_count" attrs="{'invisible': [('product_variant_count', '>', 1)]}" />
                    <field name="base_unit_id" options="{'no_open': True}" attrs="{'invisible': [('product_variant_count', '>', 1)]}" />
                    <span attrs="{'invisible': ['|', ('base_unit_price','=', 0), ('product_variant_count', '>', 1)]}">
                        (<field name="base_unit_price"/> / <field name="base_unit_name"/>)
                    </span>
                    <span class='text-muted' attrs="{'invisible': [('product_variant_count', '&lt;=', 1)]}">Based on variants</span>
                </div>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="product_product_normal_website_form_view">
        <field name="name">product.product.normal.view.website</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <label for="base_unit_count" groups="website_sale.group_show_uom_price"/>
                <div name="base_unit_price" groups="website_sale.group_show_uom_price" class="o_row">
                    <field name="base_unit_count"/>
                    <field name="base_unit_id" options="{'no_open': True}"/>
                    <span attrs="{'invisible': [('base_unit_price','=', 0)]}">
                        (<field name="base_unit_price"/> / <field name="base_unit_name"/>)
                    </span>
                </div>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="product_template_form_view">
        <field name="name">product.template.product.website.form</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <!-- add state field in header -->
            <div name="button_box" position="inside">
                <field name="is_published" widget="website_redirect_button" attrs="{'invisible': [('sale_ok','=',False)]}"/>
            </div>
            <group name="upsell" position="attributes">
                <attribute name="invisible">0</attribute>
            </group>
            <xpath expr="//group[@name='upsell']" position="inside">
                <field name="accessory_product_ids" widget="many2many_tags" attrs="{'invisible': [('sale_ok','=',False)]}"
                       placeholder="Suggested accessories in the eCommerce cart"/>
                <field name="alternative_product_ids" widget="many2many_tags"
                       domain="[('id', '!=', active_id), '|', ('company_id', '=', company_id), ('company_id', '=', False)]"
                       attrs="{'invisible': [('sale_ok','=',False)]}"
                       placeholder="Displayed in bottom of product pages"/>
            </xpath>
            <xpath expr="//page[@name='sales']/group[@name='sale']" position="inside">
                <group string="eCommerce Shop" name="shop" attrs="{'invisible': [('sale_ok','=',False)]}">
                    <field name="website_url" invisible="1"/>
                    <field name="website_id" options="{'no_create': True}" groups="website.group_multi_website"/>
                    <field name="website_sequence" groups="base.group_no_one"/>
                    <field name="public_categ_ids" widget="many2many_tags" string="Categories"/>
                    <field name="website_ribbon_id" groups="base.group_no_one" options="{'no_quick_create': True}"/>
                </group>
                <group name="product_template_images" string="Extra Product Media" attrs="{'invisible': [('sale_ok','=',False)]}">
                    <field name="product_template_image_ids" class="o_website_sale_image_list" context="{'default_name': name}" mode="kanban" add-label="Add a Media" nolabel="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="product_product_view_form_easy_inherit_website_sale" model="ir.ui.view">
        <field name="name">product.product.view.form.easy.inherit.website_sale</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_variant_easy_edit_view"/>
        <field name="arch" type="xml">
            <field name="standard_price" position="after">
                <label for="base_unit_count" groups="website_sale.group_show_uom_price"/>
                <div name="base_unit_price" groups="website_sale.group_show_uom_price" class="o_row">
                    <field name="base_unit_count"/>
                    <field name="base_unit_id" options="{'no_open': True}"/>
                    <span attrs="{'invisible': [('base_unit_price','=', 0)]}">
                        (<field name="base_unit_price"/> / <field name="base_unit_name"/>)
                    </span>
                </div>
            </field>
            <sheet position="inside">
                <group name="product_variant_images" string="Extra Variant Media">
                    <field name="product_variant_image_ids" class="o_website_sale_image_list" context="{'default_name': name}" mode="kanban" add-label="Add a Media" nolabel="1"/>
                </group>
            </sheet>
        </field>
    </record>

    <!-- Product ribbon -->
    <record id="product_ribbon_form_view" model="ir.ui.view">
        <field name="name">product.ribbon form view</field>
        <field name="model">product.ribbon</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="html" widget="char"/>
                            <field name="text_color"/>
                        </group>
                        <group>
                            <field name="html_class"/>
                            <field name="bg_color"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Product Public Categories -->
    <record id="product_public_category_form_view" model="ir.ui.view">
        <field name="name">product.public.category.form</field>
        <field name="model">product.public.category</field>
        <field name="arch" type="xml">
            <form string="Website Public Categories">
                <sheet>
                    <field name="image_1920" widget="image" class="oe_avatar" options="{'preview_image': 'image_128'}"/>
                    <div class="oe_left">
                        <group>
                            <field name="name"/>
                            <field name="parent_id"/>
                            <field name="website_id" options="{'no_create': True}" groups="website.group_multi_website"/>
                            <field name="sequence" groups="base.group_no_one"/>
                        </group>
                    </div>
                </sheet>
            </form>
        </field>
    </record>
    <record id="product_public_category_tree_view" model="ir.ui.view">
        <field name="name">product.public.category.tree</field>
        <field name="model">product.public.category</field>
        <field name="field_parent" eval="False"/>
        <field name="arch" type="xml">
            <tree string="Product Public Categories">
                <field name="sequence" widget="handle"/>
                <field name="display_name"/>
                <field name="website_id" groups="website.group_multi_website"/>
            </tree>
        </field>
    </record>
    <record id="product_public_category_action" model="ir.actions.act_window">
        <field name="name">eCommerce Categories</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.public.category</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" eval="False"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Define a new category
          </p><p>
            Categories are used to browse your products through the
            touchscreen interface.
          </p>
        </field>
    </record>

    <record id="product_ribbon_view_tree" model="ir.ui.view">
        <field name="name">product.ribbon.tree</field>
        <field name="model">product.ribbon</field>
        <field name="arch" type="xml">
            <tree string="Products Ribbon">
                <field name="html" string="Name"/>
            </tree>
        </field>
    </record>

    <record id="website_sale_pricelist_form_view" model="ir.ui.view">
        <field name="name">website_sale.pricelist.form</field>
        <field name="inherit_id" ref="product.product_pricelist_view" />
        <field name="model">product.pricelist</field>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='pricelist_availability']" position="after">
                <group name="pricelist_website" string="Website">
                    <field name="website_id" options="{'no_create': True}"/>
                    <field name="selectable"/>
                    <field name="code"/>
                </group>
            </xpath>
          </field>
    </record>

    <record id="website_sale_pricelist_tree_view" model="ir.ui.view">
        <field name="name">product.pricelist.tree.inherit.product</field>
        <field name="model">product.pricelist</field>
        <field name="inherit_id" ref="product.product_pricelist_view_tree"/>
        <field name="arch" type="xml">
            <field name="currency_id" position="after">
                <field name="selectable" />
                <field name="website_id" groups="website.group_multi_website"/>
            </field>
        </field>
    </record>

    <!-- This view should only be used from the product o2m because the required field product_tmpl_id has to be automatically set. -->
    <record id="view_product_image_form" model="ir.ui.view">
        <field name="name">product.image.view.form</field>
        <field name="model">product.image</field>
        <field name="arch" type="xml">
            <form string="Product Images">
                <field name="sequence" invisible="1"/>
                <div class="row o_website_sale_image_modal">
                    <div class="col-md-6 col-xl-5">
                        <label for="name" string="Image Name"/>
                        <h2><field name="name" placeholder="Image Name"/></h2>
                        <label for="video_url" string="Video URL"/><br/>
                        <field name="video_url"/><br/>
                    </div>
                    <div class="col-md-6 col-xl-7 text-center o_website_sale_image_modal_container">
                        <div class="row">
                            <div class="col">
                                <field name="image_1920" widget="image"/>
                            </div>
                            <div class="col" attrs="{'invisible': [('video_url', 'in', ['', False])]}">
                                <div class="o_video_container p-2">
                                    <span>Video Preview</span>
                                    <field name="embed_code" class="mt-2" widget="video_preview"/>
                                    <h4 class="o_invalid_warning text-muted text-center" attrs="{'invisible': [('embed_code', '!=', False)]}">
                                        Please enter a valid Video URL.
                                    </h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </field>
    </record>
    <record id="product_image_view_kanban" model="ir.ui.view">
        <field name="name">product.image.view.kanban</field>
        <field name="model">product.image</field>
        <field name="arch" type="xml">
            <kanban string="Product Images" default_order="sequence">
                <field name="id"/>
                <field name="name"/>
                <field name="image_1920"/>
                <field name="sequence" widget="handle"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="card oe_kanban_global_click p-0">
                            <div class="o_squared_image">
                                <img class="card-img-top" t-att-src="kanban_image('product.image', 'image_1920', record.id.raw_value)" t-att-alt="record.name.value"/>
                            </div>
                            <div class="card-body p-0">
                                <h4 class="card-title p-2 m-0 bg-200">
                                    <small><field name="name"/></small>
                                </h4>
                            </div>
                            <!-- below 100 Kb: good -->
                            <t t-if="record.image_1920.raw_value.length &lt; 100*1000">
                                <t t-set="size_status" t-value="'badge-success'"/>
                                <t t-set="message">Acceptable file size</t>
                            </t>
                            <!-- below 1000 Kb: decent -->
                            <t t-elif="record.image_1920.raw_value.length &lt; 1000*1000">
                                <t t-set="size_status" t-value="'badge-warning'" />
                                <t t-set="message">Huge file size. The image should be optimized/reduced.</t>
                            </t>
                            <!-- above 1000 Kb: bad -->
                            <t t-else="1">
                                <t t-set="size_status" t-value="'badge-danger'"/>
                                <t t-set="message">Optimization required! Reduce the image size or increase your compression settings.</t>
                            </t>
                            <span t-attf-class="badge #{size_status} o_product_image_size" t-esc="record.image_1920.value" t-att-title="message"/>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

</odoo>
