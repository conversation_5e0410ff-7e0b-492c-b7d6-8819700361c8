<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

<!-- TODO remove in master, this is a leftover from the generalization of search bars -->
<!-- Products Search Bar autocomplete item -->
<div t-name="website_sale.productsSearchBar.autocomplete"
    class="dropdown-menu show w-100">
    <t t-if="!products.length">
        <!-- TODO adapt in master, this is patched in JS so that text-muted -->
        <!-- is not on the same element as dropdown-item-text -->
        <span class="dropdown-item-text text-muted">No results found. Please try another search.</span>
    </t>
    <a t-foreach="products" t-as="product"
        t-att-href="product['website_url']" class="dropdown-item p-2 text-wrap">
        <div class="media align-items-center o_search_product_item">
            <t t-if="widget.displayImage">
                <img t-attf-src="/web/image/product.template/#{product['id']}/image_128"
                    class="flex-shrink-0 o_image_64_contain"/>
            </t>
            <div class="media-body px-3">
                <t t-set="description" t-value="widget.displayDescription and product['description_sale']"/>
                <h6 t-attf-class="font-weight-bold #{description ? '' : 'mb-0'}" t-out="product['name']"/>
                <p t-if="description" class="mb-0" t-out="description"/>
            </div>
            <div t-if="widget.displayPrice" class="flex-shrink-0">
                <t t-if="product['has_discounted_price']">
                    <span class="text-danger text-nowrap" style="text-decoration: line-through;">
                        <t t-out="product['list_price']"/>
                    </span>
                    <br/>
                </t>
                <b class="text-nowrap">
                    <t t-out="product['price']"/>
                </b>
            </div>
        </div>
    </a>
    <t t-if="hasMoreProducts">
        <button type="submit" class="dropdown-item text-center text-primary">All results</button>
    </t>
</div>

<!-- Products Search Bar autocomplete item -->
<we-button t-name="website_sale.ribbonSelectItem" t-att-data-set-ribbon="ribbon.id">
    <t t-out="ribbon.html"/>
    <span t-attf-class="fa fa-#{isTag ? 'tag' : 'bookmark'} ml-auto"></span>
    <span t-attf-class="fa fa-arrow-#{isLeft ? 'left' : 'right'} ml-1"></span>
    <span t-attf-class="o_wsale_color_preview #{colorClasses} ml-1" t-attf-style="background-color: #{ribbon.bg_color}"></span>
    <span t-attf-class="o_wsale_color_preview #{colorClasses} ml-1" t-attf-style="background-color: #{textColor} !important;"></span>
</we-button>

</templates>
