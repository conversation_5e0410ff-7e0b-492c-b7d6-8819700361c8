.o_dashboard_sales {

    h2 {
        padding: 15px;
    }
    h4 { margin: 3px 0 4px 0 !important; }
    .o_demo_background {
        margin-top: 16px;
        height: 350px;
        background-size: 100% !important;
        background: url("/website_sale/static/src/img/website_sale_dashboard_sales_demo.png") no-repeat;
        position: relative;
        opacity: 0.2;
    }
    .o_demo_message {
        color: $o-main-color-muted;
        width: 100%;
        @include o-position-absolute($left: 0, $top: 12%);
        display: flex;
        justify-content: center;
    }
    .o_link_enable {
        @include media-breakpoint-up(lg) {
            border-right: 1px solid #ccc;
        }
        height: initial;
        padding: 5px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        a {
            color: darken($o-brand-lightsecondary, 40%);
        }
        &:hover {
            background-color: $o-brand-lightsecondary;
        }
        .o_highlight {
            color: $o-brand-primary;
            font-size: 20px;
            font-weight: bold;
        }
    }
    .o_link_disable {
        @include media-breakpoint-up(lg) {
            border-right: 1px solid #ccc;
        }
        height: initial;
        padding: 5px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 10px 0px;
        color: darken($o-brand-lightsecondary, 40%);
        .o_highlight {
            font-size: 20px;
            font-weight: bold;
        }
    }
    .o_invisible_border {
        border-right: 0px;
    }
    .o_top_margin {
        margin-top: 20px;
    }
    .o_graph_canvas_container {
        position: relative;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 30em;
    }
    .o_dashboard_utms {
        .utm_chart_image {
            display: block;
            margin: auto;
        }
        .utm_dropdown {
            margin-top: -5px;
        }
    }
}