# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# g<PERSON><PERSON> <<EMAIL>>, 2022
# ka<PERSON><PERSON><PERSON> <karolina.schustero<PERSON>@vdp.sk>, 2022
# <PERSON><PERSON><PERSON> <jaro.b<PERSON><PERSON>@ekoenergo.sk>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr "Čas odchodu nesmie by skôr ako čas príchodu."

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%(empl_name)s from %(check_in)s"
msgstr "%(empl_name)s od %(check_in)s"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%(empl_name)s from %(check_in)s to %(check_out)s"
msgstr "%(empl_name)s od %(check_in)s do %(check_out)s"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid ""
"<b>Warning! Last check in was over 12 hours ago.</b><br/>If this isn't "
"right, please contact Human Resource staff"
msgstr ""
"<b>Pozor! Posledný príchod bol pred viac ako 12 hodinami.</b><br/>Ak to "
"neplatí, kontaktujte pracovníkov HR"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_kanban
msgid "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Perióda\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Employee PIN</span>"
msgstr "<span class=\"o_form_label\">PIN zamestnanca</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Month\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Minulý mesiac\n"
"                        </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Attendance</span>"
msgstr "<span class=\"o_stat_text\">Prítomnosť</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Attended Since</span>"
msgstr "<span class=\"o_stat_text\">Zúčastnil sa od</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Extra Hours</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Not Attended Since</span>"
msgstr "<span class=\"o_stat_text\">Neúčastnil sa od</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle "
"oe_hr_attendance_status_green\" role=\"img\" aria-label=\"Available\" "
"title=\"Available\"/>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle "
"oe_hr_attendance_status_green\" role=\"img\" aria-label=\"Dostupný\" "
"title=\"Dostupný\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle oe_hr_attendance_status_orange\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle oe_hr_attendance_status_orange\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> Minutes</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span> Minutes</span>\n"
"                                    <br/>\n"
"                                    <br/>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span>Time Period </span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Activate the count of employees' extra hours."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""
"Pridaj zopár zamestnancov, aby bolo možné z nich vybrať a vykonať kontrolu príchodu/odchodu.\n"
" Pre vytvorenie zamestnanca choď od menu Zamestnanci."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__adjustment
msgid "Adjustment"
msgstr "Úprava"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Administrator"
msgstr "Správca"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"counted, in benefit of the company"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"deducted, in benefit of the employee"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
msgid "Amount of extra hours"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "An apple a day keeps the doctor away"
msgstr "Jablko denne odplaší lekára"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Another good day's work! See you soon!"
msgstr "Ďalšia dobrá práca! Uvidíme sa čoskoro!"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_my_attendances
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_pivot
msgid "Attendance"
msgstr "Dochádzka"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_report_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_report_action_filtered
msgid "Attendance Analysis"
msgstr "Analýza dochádzky"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_overtime
msgid "Attendance Overtime"
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_report
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_graph
msgid "Attendance Statistics"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_state
msgid "Attendance Status"
msgstr "Stav prítomnosti"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_employee
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_overview
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_kiosk_mode
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_attendances_overview
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr "Dochádzky"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
#, python-format
msgid "Available"
msgstr "Dostupné"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr ""
"Nemožno vytvoriť nový záznam dochádzky pre %(empl_name)s, zamestnanec sa "
"ešte neodhlásil od %(datetime)s"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr ""
"Nemožno vytvoriť nový záznam dochádzky pre %(empl_name)s, zamestnanec je už "
"prihlásený %(datetime)s"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""
"Nemožno zadať odchod pre %(empl_name)s, nedá sa nájsť príslušný príchod. "
"Vaša prítomnosť bola pravdepodobne modifikovaná ručne HR."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_in
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check In"
msgstr "Príchod"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_my_attendances
msgid "Check In / Check Out"
msgstr "Príchod / Odchod"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check Out"
msgstr "Odchod"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Check-In/Out"
msgstr "Príchod / Odchod"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_in
msgid "Checked in"
msgstr "Prítomný"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked in at"
msgstr "Prítomný od"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_out
msgid "Checked out"
msgstr "Odhlásený"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked out at"
msgstr "Odhlásený od"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Click to"
msgstr "Klikni"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_company
msgid "Companies"
msgstr "Spoločnosti"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__company_id
msgid "Company"
msgstr "Spoločnosť"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Company Logo"
msgstr "Logo spoločnosti"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Compare attendance with working hours set on employee."
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr "Konfigurácia"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__hr_attendance_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__hr_attendance_overtime
msgid "Count Extra Hours"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Count of Extra Hours"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Count of extra hours is considered from this date. Potential extra hours "
"prior to this date are not considered."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "Create a new employee"
msgstr "Vytvoriť nového zamestnanca"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__date
msgid "Day"
msgstr "Deň"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__department_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__department_id
msgid "Department"
msgstr "Oddelenie"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Early to bed and early to rise, makes a man healthy, wealthy and wise"
msgstr ""
"Ísť skoro do postele a skoro vstávať, robí človeka zdravým, bohatým a múdrym"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Eat breakfast as a king, lunch as a merchant and supper as a beggar"
msgstr "Raňajkujte ako kráľ, obedujte ako obchodník a večerajte ako žobrák"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid "Employee"
msgstr "Zamestnanec"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__group_attendance_use_pin
msgid "Employee PIN"
msgstr "PIN zamestnanca"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Employee attendances"
msgstr "Dochádzky zamestnanca"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
msgid "Employees"
msgstr "Zamestnanci"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_use_pin
msgid "Enable PIN use"
msgstr "Povoliť použitie PIN"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Error: could not find corresponding employee."
msgstr "Chyba: príslušný zamestnanec nenájdený."

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_overtime_action
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__overtime_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Extra Hours"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra Hours (Real)"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_start_date
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_start_date
msgid "Extra Hours Starting Date"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra-hours including the threshold duration"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "First come, first served"
msgstr "Kto skôr príde, bude skôr obslúžený"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Glad to have you back, it's been a while!"
msgstr "Radi vás vidíme naspäť!"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Go back"
msgstr "Návrat"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good afternoon"
msgstr "Dobré popoludnie"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good evening"
msgstr "Dobrý večer"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good morning"
msgstr "Dobré ráno"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good night"
msgstr "Dobrú noc"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Goodbye"
msgstr "Dovidenia"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Group By"
msgstr "Zoskupiť podľa"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
msgid "HR Attendance Search"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good afternoon"
msgstr "Prajeme príjemné popoludnie"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good day!"
msgstr "Prajeme dobrý deň!"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good evening"
msgstr "Prajeme dobrý večer"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a nice lunch!"
msgstr "Prajeme dobrú chuť!"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Hours"
msgstr "Hodiny"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month
msgid "Hours Last Month"
msgstr "Hodiny za minulý mesiac"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month_display
msgid "Hours Last Month Display"
msgstr "Zobrazenie hodín za minulý mesiac"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_today
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_today
msgid "Hours Today"
msgstr "Hodiny dnes"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__worked_hours
msgid "Hours Worked"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr "Hľadať HR dochádzku"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__id
msgid "ID"
msgstr "ID"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Identify Manually"
msgstr "Identifikujte manuálne"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "If a job is worth doing, it is worth doing well!"
msgstr "Ak sa prácu oplatí robiť, oplatí sa ju robiť dobre!"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Invalid request, please return to the main menu."
msgstr "Neplatný dotaz, vráťte sa do hlavného menu"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_kiosk
msgid "Kiosk Attendance"
msgstr "Kiosková dochádzka"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_kiosk_no_user_mode
msgid "Kiosk Mode"
msgstr "Mód Kiosk"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_attendance_id
msgid "Last Attendance"
msgstr "Posledná prítomnosť"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance
msgid "Manual Attendance"
msgstr "Manuálna prítomnosť"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr "Správa"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr "Moja dochádzka"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "No Check Out"
msgstr "Žiadne odhlásenie"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
msgid "No attendance records found"
msgstr "Nenašiel sa žiadny záznam o dochádzke"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_overview
msgid "No attendance records to display"
msgstr "Žiadny záznam o dochádzke na zobrazenie"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "No employee corresponding to Badge ID '%(barcode)s.'"
msgstr "Žiadny zamestnanec nezodpovedá ID vysačky'%(barcode)s.'"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Not available"
msgstr "Nedostupné"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_user
msgid "Officer"
msgstr "Dôstojník"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__overtime_ids
msgid "Overtime"
msgstr "Nadčasy"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please enter your PIN to"
msgstr "Zadaj prosím PIN"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please return to the main menu."
msgstr "Vráť sa prosím na hlavné menu"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_public
msgid "Public Employee"
msgstr "Verejný zamestnanec"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_report
msgid "Reporting"
msgstr "Prehľady"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Scan your badge"
msgstr "Zosnímaj kartu"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr "Nastaviť PIN kód v detaile zamestanca (v záložke HR nastavení)"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr "Nastavenia"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Sign in"
msgstr "Prihlásiť sa"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Sign out"
msgstr "Odhlásiť sa"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Start from"
msgstr "Začať od"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "Such grouping is not allowed."
msgstr "Takéto zoskupenie nie je povolené."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
msgid "The attendance records of your employees will be displayed here."
msgstr "Tu budú zobrazené záznamy o dochádzke vašich zamestnancov."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "The early bird catches the worm"
msgstr "Ranné vtáča ďalej doskáče"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_kiosk
msgid ""
"The user will be able to open the kiosk mode and validate the employee PIN."
msgstr "Používateľ bude môcť otvoriť kioskový režim a overiť PIN zamestnanca."

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance
msgid ""
"The user will gain access to the human resources attendance menu, enabling "
"him to manage his own attendance."
msgstr ""
"Užívateľ dostane prístup k bodu menu \"Dochádzka\", aby si mohol spravovať "
"vlastnú dochádzku"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_use_pin
msgid ""
"The user will have to enter his PIN to check in and out manually at the "
"company screen."
msgstr "Užívateľ musí zadať svoj PIN pre zaznačenie príchodu a odchodu"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"To activate Kiosk mode without pin code, you must have access right as an "
"Officer or above in the Attendance app. Please contact your administrator."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Today's work hours:"
msgstr "Dnešná pracovná doba:"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_company_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_company_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Company"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_employee_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_employee_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Employee"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__total_overtime
msgid "Total Overtime"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Total extra hours:"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes to check in in Kiosk Mode"
msgstr "Na prihlásenie v kioskom režime použite PIN kódy "

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_users
msgid "Users"
msgstr "Užívatelia"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Want to check out?"
msgstr "Chcete sa odhlásiť?"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid ""
"Warning : Your user should be linked to an employee to use attendance. "
"Please contact your administrator."
msgstr ""
"Upozornenie: Užívateľ by mal byť prepojený so zamestnancom, aby sa dala "
"určiť prítomnosť. Kontaktujte správcu."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome"
msgstr "Vitajte"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome to"
msgstr "Vitajte v"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome!"
msgstr "Vitajte!"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Work Hours"
msgstr "Odpracovaný čas"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__worked_hours
msgid "Worked Hours"
msgstr "Odpracované hodiny"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Worked hours last month"
msgstr "Odpracované hodiny za minulý mesiac"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "Wrong PIN"
msgstr "Nesprávny PIN kód"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "You cannot duplicate an attendance."
msgstr "Prítomnosť nemožno duplikovať."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_overview
msgid "Your attendance records will be displayed here."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check in"
msgstr "príchod"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check out"
msgstr "odchod"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__attendance_ids
msgid "list of attendances for the employee"
msgstr "zoznam pdochádzky zamestnanca"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "or"
msgstr "alebo"
