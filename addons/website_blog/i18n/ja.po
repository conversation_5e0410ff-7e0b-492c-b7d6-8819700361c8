# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# Shi<PERSON><PERSON>zu N. <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <naniwa.ma<PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Jun<PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-14 15:42+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "#{year}"
msgstr "#{year}"

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (コピー)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr "のページヘッダー。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr "'. 右記の結果を表示しています'"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr "-- 全日付"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""
"<b>双眼鏡は軽量で持ち運びに便利です。</b>自宅のデッキに天文台を設置して観測を行うような贅沢ができる人でない限り、観測を行うには旅をしなければならないでしょう。双眼鏡は持ち運びが簡単で、面倒な望遠鏡の一式よりも軽量です。"
" "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Click on Save</b> to record your changes."
msgstr "<b>クリックして保存</b> で変更が記録されます。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""
"<b>専門家の意見を聞く</b>もし、 "
"まだ天文学会や天文クラブで活動していないのであれば、望遠鏡店の販売員が地域で活動している学会を教えてくれます。望遠鏡を購入した人たちとのつながりができれば、何が有益で何を避けるべきかについて、ウェブ記事やウォルマートの販売員から得るよりも有効なアドバイスを得ることができるでしょう。"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr "訪問者に表示させるには<b>あなたのブログの投稿を公開</b>してください。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>サインイン</b>"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>That's it, your blog post is published!</b> Discover more features "
"through the <i>Customize</i> menu."
msgstr "<b>これで、あなたのブログの投稿が公開されます！</b><i>カスタマイズ</i>メニューからさらに機能を発見できます。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""
"<b>買う前に試してみましょう。</b>これも、天文クラブで遠足に行くことの利点の一つです。望遠鏡に詳しく、機材をセットアップしている人たちと有意義な時間を過ごすことで、彼らの機材についてよく知り、重要な技術的側面を学び、自分の機材にお金をつぎ込む前に試してみることができるからです。"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>あなたの記事をここに書きます</b>上部のツールバーを使用してテキストのスタイルを設定します：画像や表を追加したり、太字や斜体などを設定します。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr "<em class=\"h4 my-0\">原住民とは別に、現地の野生動物も観客を惹きつける大きな要因となっています。</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr "<em class=\"h4 my-0\">適切な望遠鏡を手に入れることは非常に重要です。</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr "<em class=\"h4 my-0\">この\"驚き！ \"の瞬間こそが、占星術の全てなのです。</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr "</em>レビューを読めば読むほど、その意見が両極端に集まる傾向があることに気づくですしょう。</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">宇宙には時代を超越した何かがあります。</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr "<em class=\"h4 my-0\">月についての研究も、他のことと同じように、単純なものから非常に複雑なものまで存在します。</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>タグが定義されていません</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Post date\" title=\"Post date\"/>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-sm\">Read "
"Next</span>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"mr-1\">Show:</span>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled pl-0\">Blogs:</span>"
msgstr ""

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "隠れた名所を発見する素晴らしい方法"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr ""
"コッパー・キャニオンでの休暇は、リラックス、文化、歴史、野生動物、ハイキングなどを多彩に取り揃え、エキサイティングなものになること間違いなしです！"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "新しい投稿"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"訪れる人は、キャニオンの周りをハイキングしたり、キャニオンの中に入ってみたりして、このエリアを探索できます。峡谷の奥深くまで行きたい場合には、綿密に計画を立てましょう。この地域へのツアーを専門に企画する旅行会社が数多くあります。コッパー・キャニオンへは、180日間有効の観光ビザで、飛行機で行くことができます。また、アメリカ国内のどこからでも車で行くことが可能で、国境にあるメキシコの税関でビザを取得することもできます。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "会社概要"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
"何よりも、商品についてよく知っている人が働いている、<b>評判の良い望遠鏡店と関係を築くこと</b>です。ウォルマートやデパートで望遠鏡を買っても、ぴったりのものが手に入る確率は低いでしょう。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "投稿にアクセス"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "有効"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "以下をいくつか追加"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "全て"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "全てのブログ"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "全てのブログ"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Alone in the ocean"
msgstr "海に独り"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "それに関連して、設定と故障の難易度は？"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.js:0
#, python-format
msgid "Amazing blog article: %s! Check it live: %s"
msgstr "素晴らしいブログ記事: %s! ライブで確認しましょう: %s"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr "リラックス、文化、歴史、野生動物、ハイキングが多彩に混ざる合った場所。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"そして、全てが終わったら、<b>装備を整え</b>ましょう。より新しく、より良い望遠鏡への探求は生涯続くでしょう。天文学に魅了されると、その経験は人生のあらゆる側面を豊かにしてくれます。あなたは決してやめたくないほど夢中になることでしょう。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"コッパー・キャニオンのもうひとつの特徴は、タラフマラ・インディアン文化の存在です。この半遊牧民は洞窟住居に住んでいます。彼らは主に農業と牧畜で生計を立てています。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
msgid "Archive"
msgstr "アーカイブ"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "アーカイブ済"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
msgid "Archives"
msgstr "アーカイブ"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "記事"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "記事"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "天文学"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"天文クラブは、知識を分かち合うのが大好きな知識豊富なアマチュアでいっぱいの活気ある場所です。コーラとおやつを買う値段で、彼らはあなたと一緒に星を見に行き、トリビアと素晴らしい知識であなたを圧倒するでしょう。"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "天文学とは \"星を眺めること\""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Atom フィード"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "著作者"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "著作者名"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "アバター"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Awesome hotel rooms"
msgstr "素晴らしいホテルの部屋"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr "\"天文学 \"というものを意識しましょう"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"熱心なアマチュア天文家のクラブの一員になることで、月を研究する能力で新たなレベルに到達するための組織的な取り組みに参加できます。そして、天文への情熱を共有し、私たちの頭上に広がる果てしない宇宙についての知識を求めて、広大な夜空、月、そしてその向こうのどこを次に見ることができるかを探し求めながら、自分の経験や専門分野を分かち合える仲間や友人を得ることができるのです。"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "熱心なアマチュア天文家クラブの一員になること。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "寝室設備"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"大きな出費をする前に、肉眼の次のステップとして双眼鏡に投資してみるのもよいでしょう。星を眺めるのに適した双眼鏡で得られる視力によって、宇宙の驚異をさらによく見ることができます。また、デザイン性の高い双眼鏡を使えば、いつでも素晴らしい景色が目の前に現れたときに、“強化された視界”を手元に置いておけるので、機動性も格段に向上します。"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "最初の買い物をする前に..."

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "視界の向こう"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog"
msgstr "ブログ"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "ブログ名"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog Post"
msgstr "ブログ投稿"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to be calling this file !"
msgstr ""

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to have a link to this page !"
msgstr "ブログ投稿 <b>%s</b> にはこのページへのリンクがあるようです。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "ブログポストカバー"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "ブログ投稿タイトル"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.actions.act_window,name:website_blog.action_blog_post
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
#, python-format
msgid "Blog Posts"
msgstr "ブログ記事"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "ブログのサブタイトル"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "ブログタグ"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "ブログタグカテゴリ"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "タグ"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "ブログタイトル"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "ブログタイトル"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr "ブログ"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"しかし、提供されている素晴らしい選択肢の中からどのようにふるいにかければいいのでしょう？さらに重要なことは、予約を取ってもらうために、ホテルが提供している写真や説明を本当に信用できるのでしょうか？旅行者のレビューは参考になりますが、注意が必要です。偏っていることも多かったり、古かったりして、全く役に立たないこともあります。レビュアーにとって重要な特徴が、自分にとって必要だとも限らないのです。"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "望遠鏡の購入"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr "天文学への情熱を次の段階へと進めるために、適した望遠鏡を購入することは、星への情熱を発展させる大きな次のステップです。"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "公開可能"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "カテゴリ"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "子供用設備"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Choose an image from the library."
msgstr "ライブラリから画像を選択。"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Click here to add new content to your website."
msgstr "ウェブサイトに新しいコンテンツを追加するには、ここをクリックして下さい。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr "右上の'新規作成'をクリックし、最初のブログを書いて下さい。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "閉じる"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "コメント"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Comments"
msgstr "コメント"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "内容"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"コッパー・キャニオンは、この地域にある6つの峡谷のひとつです。その名前から、この渓谷は銅の採掘と関係があるように思われますが、そうではありません。この名前は、峡谷を覆う赤銅色と緑の地衣類に由来します。コッパー・キャニオンには2つの気候帯が存在します。山頂部は高山気候で、下層部は亜熱帯気候です。冬は寒く、高地では吹雪が頻繁に起こります。夏は乾燥して暑くなります。首都チワワは標高の高い砂漠地帯で、冬は寒く夏は暑いです。この地域は、様々な生態系が存在するユニークな地域です。"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "表紙のプロパティ"

#. module: website_blog
#: model_terms:ir.actions.act_window,help:website_blog.action_blog_post
msgid "Create a new blog post"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "作成者"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "作成日"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "日付（新→旧）"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "日付（旧→新）"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "説明"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "デクスター"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "表示名"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_edit_options
msgid "Duplicate"
msgstr "複製"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "東マウイ"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"マウイ島東部のヘリコプターツアーでは、標高1万フィートの火山、ハレアカラ(太陽の家)を眺めることができます。この火山は休火山で、最後に噴火したのは1790年です。マウイ島のヘリコプターツアーでは、火山のクレーターや、火山の南側の傾斜地を囲む乾燥した大地を見ることができます。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "バックエンドで編集"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "以下を編集:"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "'全てのブログ'ページヘッダーを編集"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "'結果をフィルタ'ページヘッダーを編集"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "Facebook"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "心に留めておくべき事実。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"最後に、最も重要なこととして、クオリティ・ホテル・ディレクトリーの検査チームは、該当するホテルを定期的に訪れ、スタッフに会い、ベッドルームで眠り、料理を試食しているはずです。彼らは、ホテルの宿泊客だけができるようにホテルを経験します。そうすることでしか、ホテルについて書くことができるという、本当に強力な立場にいることはできないのです。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Follow Us"
msgstr "もしよかったらフォローお願いします"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"都会に住む私たちの多くは、日常的にあの空に気づくことはないのです。街の明かりが、私たちの頭上にいつもある素晴らしいディスプレイをうまく隠してしまうからです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr "私たちの多くにとって、天体について学ぶ最初の経験は、初めて満月を見たときから始まります。それは肉眼で見ても実に壮大な眺めです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"小さな赤ちゃんから最先端の天体物理学者まで、天文学を楽しみたい人なら誰でも楽しむことができます。実際、天文学はとても身近な科学であり、事実上誰でもどこにいても天文学を学ぶことができます。その方法は、ただ見上げるだけで良いのです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "オタクになる"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "望遠鏡を手に入れる"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "歴史を知る"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "はじめましょう"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "グループ化"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr "以下は、覚えておくべき重要な事実です:"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "休暇のヒント"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "検索方法"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"望遠鏡はどれくらい複雑で、手入れに困ることはないのか？このような疑問やその他の疑問に対する答えを得るために、ネットワークを構築しましょう。こうして下調べをすれば、あなたの天文への情熱を進化させる次の大きな一歩にふさわしい望遠鏡が見つかるはずです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "望遠鏡はどの程度移動可能でなければなりませんか？"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
msgid "How to choose the right hotel"
msgstr "正しいホテルの選び方"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread
msgid "If checked, new messages require your attention."
msgstr "チェックされている場合は、新しいメッセージに注意が必要です。"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合、一部のメッセージで配信エラーが発生しています。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"例えば、ホテルがビーチに面していたり、テーマパークに近かったり、空港へのアクセスが便利であったりすることが重要なのであれば、最も重要なのは立地です。まともなディレクトリであれば、ホテルとその周辺のロケーションマップを提供しているはずです。空港までの距離表やインタラクティブな地図もあるでしょう。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"夜に晴れていれば、自宅の裏庭で星を眺めるだけで、月面の驚くようなディテールを見ることができます。\n"
"もちろん、天文学への愛が深まれば、多くの天体に魅力を感じるようになります。けれども、月は、地球の近くを飛び、人類がその上を歩いたというユニークな特徴を持つ、はるか彼方の宇宙天体であるという点で、私たちが魅了される初めての星であることでしょう。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
msgid "In"
msgstr "に"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"多くの点で、それは、天文学をただ漠然と楽しんでいる人から、天文学を本格的に学ぶ人への大きな一歩です。けれども、望遠鏡の使い方を本当に知るまでには、望遠鏡を購入した後にもまだ大きなステップがあると、あなたも私も理解しているのです。"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
msgid "Is Published"
msgstr "公開済"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Islands"
msgstr "島"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"星座や夜空をナビゲートする方法、惑星や有名な星を見つける方法を学び始めるのはとても楽しいものです。ガイドとなってくれるウェブサイトや本も数多くあります。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"コンテンポラリーか伝統的か、現地の装飾か国際的か、フォーマルかリラックスかなど、心地よく過ごせるホテルを選ぶことが大切です。理想的なホテルのディレクトリには、利用可能なオプションが記載されているはずです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr "夜空に広がる宇宙の大きさに直面し、唖然とする瞬間が誰の人生にもあるはずです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"どんな夜でも見上げるだけで、何十万もの星、星系、惑星、月、小惑星、彗星、そして時折スペースシャトルが通り過ぎるかもしれません。あなたが見上げている空は、何百年、何千年前の私たちの祖先がただ見上げて楽しんでいた空と、どこからどう見て全く同じなのです。"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Jungle"
msgstr "ジャングル"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "自分が見上げているものを知りましょう"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "いつ見るべきか知りましょう"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Large"
msgstr "大"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "最終更新者"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Latest"
msgstr "最新"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_latest_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "最新ブログポスト"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"天文学の偉大な発見の背景を知ることで、星を眺める時間がより有意義なものになります。天文学は地球上で最も古い科学のひとつです。歴史上の偉人たちがどのように見てきたのかについて知りましょう。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "レジャー設備"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"地元のカラーも良いですが、ホテル直営のレストランやバーも滞在に重要な役割を果たします。選択肢やスタイル、スマートかインフォーマルかなどを意識する必要があります。特に朝食の設備については、優れたホテルのレポートで分るでしょう。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "ロケーション"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_main_attachment_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_main_attachment_id
msgid "Main Attachment"
msgstr "主な添付"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "マーリー"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Maui helicopter tours"
msgstr "マウイヘリコプターツアー"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"マウイ島のヘリコプターツアーは、違った視点から島を見たり、楽しい冒険をしたりするのに最適な方法です。ヘリコプターに乗ったことがない方には、ぜひおすすめです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"マウイ島のヘリコプターツアーは、徒歩や車では行けないような場所を観光するのに最適な方法です。ツアーの所要時間は約1時間で、料金は1人185ドルから240ドル。多くの人にとって、これは二度と見ることのできない自然の風景を見る一生に一度の機会でしょう。カメラやビデオでその瞬間を撮影しておけば、何年経っても思い出に浸りながら何度でもツアーを楽しむことができます。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"マウイ島のヘリコプターツアーでは、これらの観光スポットを全て見ることができます。マウイ島ヘリコプターツアーに参加する際は、カメラやビデオを持っていくことをお忘れなく。美しい景色を写真に収め、休暇中に見た素晴らしい景色を友人や家族に見せることができます。"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Medium"
msgstr "媒体"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "メタディスクリプション"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "メタキーワード"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "メタタイトル"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "モロカイ・マウイ島"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"モロカイ・マウイ島ヘリコプターツアーは、たった9マイルしか離れていない、飛行機で簡単にアクセスできる別の島へご案内します。この島は人口がとても少なく、文化も景色も異なります。北東の海岸一帯は断崖絶壁で、人里離れたビーチが並んでいます。飛行機以外の交通手段では全くアクセスできない場所なのです。\n"
"島に住む人々は、マウイのヘリコプターツアーに参加しない限り、この素晴らしい景色を見たことすらないのです。天候が雨で、その季節に降雨量が多いときは、驚くような滝を数多く見ることができます。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"出張者よりも家族旅行者にとって重要なのは、そのホテルがどの程度子供に優しいかディレクトリから調べ、そこから判断することです。探す価値があるのは、ホテルがベビーシッターサービスを提供しているかどうかです。子連れを避けたいビジネス旅行者にとっても、これはもちろん非常に重要なことです。おそらく、子供に優しくないホテルであるということがより適切になるでしょう！"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_most_viewed_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "最も閲覧されたブログポスト"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "名称"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "New Blog Post"
msgstr "新しいブログ投稿"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "ブログ投稿はまだありません。"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "ビュー数"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr " \"%s\"の結果がありません。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "次の検索に結果が見つかりませんでした:「"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "タグがまだ定義されていません。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "なし"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"このどれもあなたが素晴らしい望遠鏡システムを構築する計画を進めることを妨げにはなりません。ただ、自分のニーズに合った望遠鏡の設定方法について、質の高いアドバイスとトレーニングを受けるようにしましょう。これらのガイドラインを使えば、肉眼では見ることのできない夜空の驚異的な光景を、何時間でも星空観察で楽しむことができるでしょう。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"天気を知ることで星空観察が実りあるものになるだけでなく、大きな流星群やその他の天文学のビッグイベントがいつ起こるかを知れば、天文学の興奮をより生き生きと感じられるようになるでしょう。"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "アクションの数"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "アクションを必要とするメッセージの数"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーのメッセージ数"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread_counter
msgid "Number of unread messages"
msgstr "未読メッセージ件数"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"もちろん、あなたの月面観測を究極のものにするために、機材を良いスターター望遠鏡にステップアップさせれば、月面の最も素晴らしいディテールを見ることができます。こうしたアップグレードを重ねるごとに、あなたの知識、観測の深さと範囲は幾何学的に向上していきます。多くのアマチュア天文家にとって、私たちの最も身近な天体である月で見えるものだけでは満足できないのです。"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Once you have reviewed the content on mobile, close the preview."
msgstr "モバイルでコンテンツを確認したら、プレビューを閉じます。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "その他"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "ブログ"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "写真: Anton Repponen, @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "写真: Arto Marttinen, @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "写真: Boris Smokrovic, @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "写真:  Denys Nevozhai, @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr ""
"写真: \n"
"Greg Rakozy, @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "写真:  Jason Briscoe, @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "写真:  Jon Ly, @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "写真: Patrick Brinksma, @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "写真: PoloX Hernandez, @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "写真:  SpaceX, @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "写真: Teddy Kelley, @teddykelley"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "投稿"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "公開日"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "公開済 ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "公開日"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "公開済投稿"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "公開オプション"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "公開日"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Read more"
msgstr "続きを読む"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"fa fa-chevron-right ml-2\"/>"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "レストラン、カフェ、バー"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict publishing to this website."
msgstr "このWebサイトへの公開を制限します。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO最適化"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "サンプル"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Satellites"
msgstr "サテライト"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Search for an image. (eg: type \"business\")"
msgstr "画像を検索 (例: \"ビジネス\"と入力)"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seaside vs mountain side"
msgstr "海側 vs 山側"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seeing the world from above"
msgstr "上から世界を眺める"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "Select Blog"
msgstr "ブログを選択"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select the blog you want to add the post to."
msgstr "記事を追加したいブログを選択します。"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr "新しいブログ投稿を作成するには、このメニュー項目を選択します。"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "ソ名"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "コンマでキーワードごとに区切ります。"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Set a blog post <b>cover</b>."
msgstr "ブログの投稿の<b>表紙</b>を設定してください。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"数種類の渡り鳥や在来種の鳥類、哺乳類、爬虫類がコッパーキャニオンを住処としています。手つかずの自然が残るこの土地に生息する見事な動物相も一見の価値があります。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "Facebook上で共有"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "LinkedInで共有"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Twitter"
msgstr "Twitterで共有"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "このポストをシェア"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
msgid "Sierra Tarahumara"
msgstr "シエラタラフマラ"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr "シエラタラフマラ、通称コッパーキャニオンはメキシコにあります。この地域は、冒険的な休暇を求める人々の間で人気のある目的地です。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Silly-Chico"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Skies"
msgstr "スキー"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"そのため、自分が居る場所や星を見るときの好みに合った望遠鏡を手に入れることが非常に重要です。まずは、3つの主要な望遠鏡の種類について説明し、より正しいもの購入できる確率を上げるため、\"Telescope"
" 101″のコンセプトをご説明しましょう。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"年に一度の休暇でキャンプ場に行ったり、田舎の親戚の家に遊びに行ったりするとき、ふと気がつくと外で気前の良い夜空が壮大なショーを繰り広げていることがあります。夜空が私たちに見せてくれる気前の良さに、文字通り息をのむような衝撃を受けたことがある人なら、おそらく自分が見たものに「すごい！」としか言いようがなかったその瞬間を正確に覚えているはずです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"そのため、適切な種類の望遠鏡を選ぶには、望遠鏡を使う目的が重要です。レンズや望遠鏡のデザインだけでなく、さまざまな星を眺めるシチュエーションでの望遠鏡の性能の長所と短所を本当に理解するには、前もって下調べをし、さまざまな種類の望遠鏡に触れておくのが一番です。ですから、最初に購入する前に..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr "海外に行くことになったあなたは、目的地を決め、次はホテルを選ばなければなりません。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr " <cite title=\"Source Title\">ソースタイトル</cite>で有名な誰か"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Spotting the fauna"
msgstr "動物相の観察"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/models/website_blog.py:0
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Start writing here..."
msgstr "ここで書き始めます..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "スタイル"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "サブタイトル"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "サブタイトル"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "タグカテゴリ"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "タグカテゴリ"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr "カテゴリフォームをタグする"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "タグ フォーム"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "タグ リスト"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists !"
msgstr ""

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists !"
msgstr "そのタグ名は既に使われています！"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "タグ"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Taking pictures in the dark"
msgstr "暗闇で写真を撮る"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
msgid "Teaser"
msgstr "ティーザー"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "ティーザーのコンテンツ"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"10年前なら、おそらく地元の旅行代理店を訪れ、いわゆる'専門家'と呼ばれる人たちによる対面のアドバイスを信用したことでしょう。21世紀のホテル選びと予約の方法は、もちろんインターネットで、旅行ウェブサイトを利用することです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"その「すごい！」という瞬間こそが、占星術の全てなのです。その瞬間が情熱となり、星を研究する仕事に就く人もいます。幸運な一部の人たちは、その「すごい！」という瞬間が、スペースシャトルや初期の宇宙ミッションで星々を旅することにつながる、すべてを飲み込むほどの執着心につながることもあります。けれども、私たちの多くは、占星術を娯楽や普通の趣味として楽しむようになるでしょう。それでも、私たちはその感動を一生持ち続け、毎晩私たちの頭上にある何百万もの星々の中に見える壮大な宇宙について、もっと深く見たり学んだりする方法を探し始めるのです。"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr "天文学の素晴らしさは、誰にでもできることです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"月を眺めるのに最適なのは、雲が少なく、長く観察できる天候の夜であることは言うまでもありません。初めの学期に、最も詳細な研究を行います。また、満月でないときに月の一部が滲んで見えることがあるが、騙されてはいけません。“地球照”"
" として知られる現象により、たとえ月が4分の1または半分しか見えていなくても、月の暗くなった部分をある程度詳細に見ることができるのです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "月を眺めるのに最適な時間"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr "ブログ記事は、公開日が設定されている場合、ウェブサイト上のこの日付の時点で訪問者に表示されます。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"この地域の断崖は世界でも有数の高さを誇り、高い峰から流れ落ちる水はまさに息をのむ美しさです。マウイ島からのマウイヘリコプターツアーは短時間であっても、この美しい自然環境を見る価値があるのです。"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr "ウェブサイトを介してドキュメントにアクセスするために完全なURLです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"次に手に入れたいのは、当然ながら良い望遠鏡です。自分の研究を順調に進めているアマチュアの人が、どこかの丘の上にとても格好良く見える望遠鏡を設置しているのを見たことがあるかもしれません。それは、趣味を育てて行くための理にかなった次のステップに違いないので、あなたの中のアマチュア天文学者を興奮させます。しかし、いかにして良い望遠鏡を購入するかは、実に困惑し、重荷に感じるものです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"ホテル内のレジャーサービス(スパ、プール、ジム、サウナなど)の詳細な分析、およびゴルフコースなど近隣の施設の詳細も掲載して下さい。7. "
"特別なニーズ：ホテルディレクトリーサイトでは、各ホテルの特別なニーズに対するサービスや利用ポリシーについて、旅行者にアドバイスする必要があります。全ての旅行者に当てはまるわけではありませんが、これらも、一部の旅行者には絶対に欠かせない情報です。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr "三脚やその他のアクセサリーの選択は、自宅のデッキで使用する望遠鏡と、遠隔地に頻繁に持ち運ぶ予定の望遠鏡では大きく変わってきます。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"この景色は本当に息をのむほど美しく、見逃すことのできない光景です。また、休火山を間近に見ることができ、日頃見ることのできない貴重な体験をすることができます。火山の北側と南側では、驚くほど違った景色を見ることができるのです。これらの側面は緑豊かで、美しい滝や壮大に茂る草を見ることができます。島のこちら側には熱帯雨林が多く、飛行機以外の手段では簡単にアクセスできないのです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"それから、レビュアーの動機という問題もあります。レビューを読めば読むほど、意見が両極端に集中する傾向があることに気づくでしょう。恨みを持った怒っているレビュアーがいる一方で、驚くほどの賞賛を惜しまない大喜びのゲストがいます。ホテルが自分たちの熱烈なレビューを投稿することもあれば、競合他社が悪いレビューで他社を非難するチャンスを狙って列をなしていることを知っても驚きはしないでしょう。ホテルを選ぶ際には、自分にとって何が本当に重要なのかを考えることが大切です。そして、最新で、独立し、公平に、本当に重要な情報を提供するオンラインホテルディレクトリーを選ぶべきなのです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr "最終的に購入を決定する際には、他にも考慮すべき点があります。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""
"宇宙には時間を超越した何かがある。惑星や月、そしてその先にある星々がはるか昔からそこにあったという事実は、私たちが宇宙の中でどのような位置を占めているかということを教えてくれます。実際、私たちが肉眼で"
" “見ている” "
"星の多くは、実は何十万年も前にその星からやってきた光なのです。その光が今、地球に届いています。つまり、空を見上げることは、非常に現実的なタイムトラベルのようなものなのです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""
"こうしたことは本当に重要で、きちんとしたホテルディレクトリーであれば、通常の選択肢である部屋数だけでなく、ベッドルームについてもこのようなアドバイスをくれるでしょう！"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr "この箱は訪問者には表示されません"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "This tag already exists"
msgstr "このタグは既に存在します"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Tiny"
msgstr "小さい"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "タイトル"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""
"肉眼で月を眺めるには、月の地図に慣れ親しんでおくと、海やクレーターなど、他の人がすでに地図化した地理的現象を見つけ出すことができるので、学ぶことがより楽しくなります。月の地図は、天文ショップやオンラインショップで入手でき、投資する価値は十分にあります。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""
"星をよりよく観察する方法を学ぶには、肉眼で見えるものに加えて、より深く星を観察する、そして星を楽しむだけでなく、星を研究し始めるのに必要な基本的なものがいくつかあります。まず必要なのは、道具ではなく文献です。良い星図があれば、主要な星座、星空をナビゲートするのに使う主要な星の位置、星よりも大きく見える惑星がわかります。そしてその地図に加えて、天文学という趣味の良質な入門書があれば、道は開けるでしょう。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""
"双眼鏡があれば、月面のディテールをより鮮明に見ることができます。最高の結果を得るには、双眼鏡の設定で広視野を確保し、月の風景の美しさを余すところなく観察できるようにすることです。また、この壮大な天体を見つめている間、双眼鏡を静止させることはほぼ不可能なため、双眼鏡を固定できる三脚を機材に加えておくと、安定した観望台で快適に月を観察することができます。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""
"月をよりよく見るためには、他の天文学者との提携を利用したり、大気の影響を排除する優れた技術に投資した専門家が設置した、本当に素晴らしい望遠鏡を見学しに行くのも良いでしょう。インターネットを使えば、ハッブル望遠鏡や、常に月を観測している巨大な望遠鏡の多くにアクセスすることができます。さらに、多くの天文クラブが複数の望遠鏡を組み合わせてコンピューターと丁寧に同期させ、月のある光景を最もよく見えるようにする方法を研究しているのです。"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr "旅行"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "未公開 ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread
msgid "Unread Messages"
msgstr "未読メッセージ"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未読メッセージカウンター"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "無題の投稿"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr "このアイコンを使用して、<b>携帯端末</b>ブログ投稿をプレビューします。"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "以下で使用:"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Viewpoints"
msgstr "観点"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "ビュー"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr "全てのブログページで閲覧可能"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "現在のウェブサイトで見える状態"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "WRITE HERE OR DRAG BUILDING BLOCKS"
msgstr "ここに書き込むか、ブロックをドラッグ"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "ウェブサイト"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "ウェブサイトのブログ"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "サイトスニペットフィルタ"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "ウェブサイトURL"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイトコミュニケーション履歴"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "ウェブサイトメタディスクリプション"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "ウェブサイトメタキーワード"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "ウェブサイトメタタイトル"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "ウェブサイトopengraph画像"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr "もしハッブル望遠鏡の操作をさせてもらえたら？"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""
"いつでも誰でも星空を見上げることができ、星に恋することができますが、天文の楽しみは、見上げるたびに、より多くの星が見え、より多くのことを理解できるように、星を眺める技術を身につけることです。ここでは、天文という趣味に没頭できる時間をさらにもっと楽しくするために、あなたができるいくつかのステップをご紹介します。"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "With a View"
msgstr "眺望あり"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr "ブログや会社を説明する短い文章をここに書きましょう。"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Write a title, the subtitle is optional."
msgstr "タイトルを記入、サブタイトルはオプションです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""
"寝室にどのような設備が必要かを常に慎重に検討し、重要だと思う設備があるホテルを見つけて下さい。ベッドサイズ、インターネットアクセス(料金、WIFIか有線ブロードバンド接続か)、無料アメニティ、部屋からの眺め、ピローメニューやバスメニューのような豪華なサービス、喫煙・禁煙ルームの選択などです。"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""
"マウイ島の持つ全ての美しさを堪能し、家族全員で素晴らしい時間を過ごすことができます。ツアーはそれほど高価ではなく、所要時間は45分から1時間以上です。マウイヘリコプターツアーでは、通常アクセスできない場所を見ることができます。徒歩や乗り物では行くことができない場所を空から見ることができるのです。マウイヘリコプターツアーに参加すれば、息をのむような景色があなたを待っています。マウイ島にゆっくりと滞在されるのであれば、複数のマウイヘリコプターツアーに参加することをお勧めします。"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr "冒険"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr "ブログ。 ブログにアクセスするには、ここをクリックしてください："

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr "パンくず"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "by"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr "発見"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr "ガイド"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "ここに掲載されます"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr "ホテル"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "に"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "in <i class=\"fa fa-folder-open text-muted\"/>"
msgstr "<i class=\"fa fa-folder-open text-muted\"/>内"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "in <i class=\"fa fa-folder-open text-white-75\"/>"
msgstr "<i class=\"fa fa-folder-open text-white-75\"/>内"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr "双眼鏡"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr "コメントを残す"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "未公開"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted mr-1\"/>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr "| まだコメントがありません"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "⌙ Hover effect"
msgstr ""
