# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-14 15:42+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "#{year}"
msgstr "#{year}"

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (نسخة)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr "' ترويسة الصفحة."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr "'. يتم عرض النتائج لـ '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr "-- كافة التواريخ "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""
"<b>تتسم المناظير بخفة الوزن وسهولة النقل.</b> إذا لم يكن باستطاعتك تركيب "
"معدّات فاخرة وتجهيز مرصد على السطح، فسوف تضطر غالباً إلى السفر لأماكن مختلفة"
" حتى تتمكن من المراقبة. المناظير أكثر سهولة في النقل ويمكنك جلبها معك عندما "
"تسافر، على عكس مجموعة أجهزة التيليسكوب. "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Click on Save</b> to record your changes."
msgstr "<b>اضغط على حفظ</b> لتسجيل التغييرات."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""
"<b>اختر عقول الخبراء</b>. إذا لم تكن عضواً فعالاً بالفعل في نادي أو مجتمع "
"لعلوم الفلك، سوف يكون بوسع مندوبي المبيعات في محلات بيع التليسكوبات مساعدتك "
"للوصول إلى المجتمعات النشطة في منطقتك. بمجرد تواصلك مع أفراد قاموا بشراء "
"تيليسكوبات، سوف تكتسب الكثير من المعلومات والنصائح عن الأجهزة التي يُنصح "
"بشرائها والتي يُفضّل الابتعاد عنها، أكثر من أي مقال قد تجده على الإنترنت أو "
"مندوب مبيعات في Wal-Mart. "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr "<b>انشر مقال مدونتك</b> حتى يتمكن زوارك من مشاهدته. "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>تسجيل الدخول</b>"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>That's it, your blog post is published!</b> Discover more features "
"through the <i>Customize</i> menu."
msgstr ""
"<b>هذا كل شيء، لقد تم نشر مقال مدونتك!</b> اكتشف المزيد من الخصائص عن طريق "
"قائمة <i>التخصيص</i>. "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""
"<b>جربه قبل أن تقوم بشرائه.</b> هذه ميزة أخرى من مميزات الذهاب إلى رحلات "
"ميدانية مع نادي علوم الفلك. يمكنك قضاء بعض الوقت القيّم مع أفراد يجيدون "
"استخدام التيليسكوب ولديهم أدوات مجهزة لتفحصها عن كثب وتعلم الجوانب التقنية "
"المتعلقة بها وتجربتها قبل أن تنفق نقودك في المعدّات الخاصة بك. "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>اكتب قصتك هنا.</b> استخدم شريط الأدوات العلوي لتنسيق نصك: كإضافة صورة أو "
"جدول، أو جعل النص سميكًا أو مائلًا، إلخ. قم بسحب وإفلات الكتل البرمجية "
"الإنشائية لجعل مدوناتك أكثر رسومية."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr ""
"<em class=\"h4 my-0\">بعيداً عن السكان الأصليين، فإن الحياة البريّة المحلية "
"تُعَد معلماً جذاباً أيضاً.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr ""
"<em class=\"h4 my-0\">من المهم جداً الحصول على التيليسكوب المناسب "
"تماماً.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr ""
"<em class=\"h4 my-0\">تلك اللحظة الخاطفة للأنفاس هي ما يجعل من علوم الفلك "
"أمراً مميزاً جداً.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr ""
"<em class=\"h4 my-0\">كلما قرأت تقييمات أكثر، كلما لاحظت تضاربات الآراء "
"المتطرفة.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">هناك شيء أزلي في الكون.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr ""
"<em class=\"h4 my-0\">دراستك للقمر، كأي شيء آخر، يمكن أن تتدرج من كونها "
"بسيطة إلى شديدة التعقيد.</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>لم يتم تحديد أي علامات تصنيف</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"إلى محتوى المدونة \"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Post date\" title=\"Post date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Post date\" "
"title=\"تاريخ النشر \"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github \"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"إنستغرام \"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn \"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS \"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"تويتر \"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"يوتيوب \"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-sm\">Read "
"Next</span>"
msgstr ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-sm\">قراءة "
"التالي</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"قراءة التالي \"/>\n"
"                                </span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"mr-1\">Show:</span>"
msgstr "<span class=\"mr-1\">إظهار:</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled pl-0\">Blogs:</span>"
msgstr "<span class=\"nav-link disabled pl-0\">المدونات:</span>"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "طريقة رائعة لاستكشاف الأماكن الخفية "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr ""
"من المؤكد أن رحلة إلى كوبر كانيون ستحمل الكثير من المتعة والحماس والاسترخاء،"
" بالإضافة إلى التعرف على الثقافة والتاريخ والاستكشاف مشياً على الأقدام. "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "منشور جديد "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"قد يرغب المسافر في استكشاف المنطقة مشياً على الأقدام حول الوادي أو عبره. سوف"
" يحتاج هؤلاء الذين يرغبون في التوغّل في الوادي تخطيطاً محكماً ومفصّلاً. توجد"
" العديد من وكالات السفر التي تتخصص في تنظيم جولات سياحية لتلك المنطقة، وبوسع"
" المسافرين السفر إلى كوبر كانيون عن طريق تأشيرة سياح والتي تكون صالحة لمدة "
"180 يوماً. بمقدور المسافرين أيضاً السفر براً من أي مكان في الولايات المتحدة "
"الأمريكية والحصول على تأشيرة في مركز الجمارك المكسيكي على الحدود. "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "من نحن"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
"والأهم من كل ذلك، <b>عليك أن تبني علاقة جيدة مع محل لبيع التيليسكوبات ذي "
"سمعة جيدة والذي موظفوه على دراية كاملة بكل ما تحتاج لمعرفته عن الأمر.</b> "
"إذا قمت بشراء التيليسكوب الخاص بك من متجر Wal-Mart، قد لا تحصل دائماً على "
"الشيء الصحيح. "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "الوصول للمنشور "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "يتطلب إجراء "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "نشط"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "إضافة بعض"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "الكل"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "كافة المدونات"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "كافة المدونات"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Alone in the ocean"
msgstr "وحدك في المحيط "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "على هذا المنوال، ما مدى صعوبة التركيب والتفكيك؟ "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.js:0
#, python-format
msgid "Amazing blog article: %s! Check it live: %s"
msgstr "مقالة مدونة مذهلة: %s! ألقِ نظرة عليها مباشرةً: %s "

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr ""
"مزيج مثير من الاسترخاء والثقافة والتاريخ والحياة البرية والمشي لمسافات "
"طويلة."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"وعندما يتم قول وفعل كل شيء ، <b>استعد</b> . إن سعيكم للحصول على تلسكوبات "
"أحدث وأفضل سوف يستمر مدى الحياة. دع نفسك تدمن علم الفلك وستثري التجربة كل "
"جانب من جوانب الحياة. سيكون إدمانًا لا تريد التخلص منه أبدًا."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"ميزة فريدة أخرى من كوبر كانيون هو وجود ثقافة التراهامارا الهندية. يعيش هؤلاء"
" الناس شبه الرحل في مساكن الكهوف. يعتمد مصدر رزقهم بشكل أساسي على الزراعة "
"وتربية الماشية."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
msgid "Archive"
msgstr "أرشفة"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "مؤرشف"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
msgid "Archives"
msgstr "الأرشيف"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "مقال"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "المقالات"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "الفلك"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"نوادي علم الفلك هي أماكن حيوية مليئة بالهواة المطلعين الذين يحبون مشاركة "
"معارفهم معك. كل ما يتطلبه الأمر هو بعض الكولا والوجبات الخفيفة، وسوف يذهبون "
"معك إلى النجوم ويغمرونك بالمعلومات البديهية والمعرفة العظيمة. "

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "علم الفلك هو \"مراقبة النجوم\""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "موجز Atom "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "الكاتب "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "اسم الكاتب"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "الصورة الرمزية"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Awesome hotel rooms"
msgstr "غرف فنادق رائعة "

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr "احذر من هذا الشيء الذي يسمى \"علم الفلك\""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"ستمنحك الانضمام إلى مجتمع علماء الفلك الهواة المخلصين إمكانية الوصول إلى هذه"
" الجهود المنظمة للوصول إلى مستويات جديدة في قدرتنا على دراسة قمر الأرض. وسوف"
" يمنح أقرانك وأصدقائك الذين يشاركونك شغفك بعلم الفلك والذين يمكنهم مشاركة "
"خبراتهم ومجالات خبرتهم أثناء سعيك للعثور على المكان الذي قد تنظر إليه بعد "
"ذلك في سماء الليل الضخمة ، وفي القمر وما بعده في سعيك وراء المعرفة حول الكون"
" الذي يبدو لا نهاية له فوقنا."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "أن تصبح جزءًا من مجتمع علماء الفلك الهواة المخلصين."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "مرافق غرفة النوم"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"قبل أن تذهب إلى هذه النفقات الكبيرة ، قد تكون الخطوة التالية أفضل من العين "
"المجردة للاستثمار في مجموعة جيدة من المناظير. حتى أن هناك مناظير مناسبة "
"للتحديق في النجوم والتي ستؤدي وظيفة جيدة في منحك تلك الرؤية الإضافية التي "
"تريد أن ترى عجائب الكون بشكل أفضل قليلاً. تمنحك مجموعة المناظير المصممة "
"جيدًا أيضًا قدرًا أكبر من الحركة والقدرة على الحفاظ على \"رؤيتك المحسنة\" في"
" متناول يدك عندما يقدم لك هذا المنظر الرائع نفسه."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "قبل إجراء عملية شرائك الأولى..."

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "ما وراء العين"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog"
msgstr "المدونة"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "اسم المدونة"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog Post"
msgstr "منشور المدونة "

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to be calling this file !"
msgstr "يبدو أن منشور المدونة <b>%s</b> يتطلب استخدام هذا الملف! "

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to have a link to this page !"
msgstr "يبدو أن منشور المدونة <b>%s</b> يحتوي على رابط لهذه الصفحة! "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "غلاف منشور المدونة"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "عنوان منشور المدونة "

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.actions.act_window,name:website_blog.action_blog_post
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
#, python-format
msgid "Blog Posts"
msgstr "منشورات المدونة "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "العنوان الفرعي للمدونة"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "علامة تصنيف المدونة "

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "فئة علامة تصنيف المدونة "

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "علامات تصنيف المدونة "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "عنوان المدونة"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "عنوان المدونة"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr "المدونات"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"ولكن كيف يمكنك التدقيق في الخيارات الرائعة المعروضة؟ والأهم من ذلك ، هل تثق "
"حقًا في صور وأوصاف الفنادق التي منحوها لأنفسهم دافعًا للحصول على الحجوزات؟ "
"يمكن أن تكون تعليقات المسافرين مفيدة ، ولكن عليك توخي الحذر. غالبًا ما تكون "
"متحيزة ، وأحيانًا تكون قديمة ، وقد لا تخدم اهتماماتك على الإطلاق. كيف تعرف "
"أن الميزات التي تهم المراجع مهمة بالنسبة لك؟"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "شراء تلسكوب"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr ""
"يعد شراء التلسكوب المناسب لنقل حبك لعلم الفلك إلى المستوى التالي خطوة تالية "
"كبيرة في تطوير شغفك بالنجوم."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "بإمكانه النشر "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "الفئة "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "مرافق الأطفال"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Choose an image from the library."
msgstr "اختر صورة من المكتبة."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Click here to add new content to your website."
msgstr "انقر هنا لإضافة محتوى جديد إلى موقعك الإلكتروني. "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr ""
"انقر فوق \" <b>جديد</b> \" في الزاوية العلوية اليسرى لكتابة أول منشور لك في "
"المدونة."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "إغلاق "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "تعليق"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Comments"
msgstr "التعليقات"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "المحتوى"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"وادي النحاس (كوبر كانيون) هو أحد التخوم الستة في المنطقة. على الرغم من أن "
"الاسم يوحي بأن الوادي قد يكون له بعض الصلة بتعدين النحاس ، إلا أن هذا ليس هو"
" الحال. الاسم مشتق من النحاس والأشنة الخضراء التي تغطي الوادي. يحتوي كوبر "
"كانيون على منطقتين مناخيتين. تتميز المنطقة بمناخ جبال الألب في الأعلى ومناخ "
"شبه استوائي في المستويات الدنيا. الشتاء بارد مع عواصف ثلجية متكررة على "
"الارتفاعات العالية. الصيف جاف وساخن. العاصمة ، تشيواوا ، هي صحراء شاهقة "
"الارتفاع حيث يتراوح الطقس فيها من الشتاء البارد إلى الصيف الحار. المنطقة "
"فريدة من نوعها بسبب النظم البيئية المختلفة الموجودة داخلها."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "خصائص الغلاف"

#. module: website_blog
#: model_terms:ir.actions.act_window,help:website_blog.action_blog_post
msgid "Create a new blog post"
msgstr "إنشاء منشور مدونة جديد"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "التاريخ (من الأحدث إلى الأقدم) "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "التاريخ (من الأقدم إلى الأحدث) "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "الوصف"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "دكستر"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_edit_options
msgid "Duplicate"
msgstr "استنساخ "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "شرق ماوي"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"ستمنحك جولات الهليكوبتر في شرق ماوي إطلالة على بركان هاليكالا أو بيت الشمس "
"الذي يبلغ طوله عشرة آلاف قدم. هذا البركان خامد واندلع آخر مرة في عام 1790. "
"ستتمكن من رؤية فوهة البركان والأرض الجافة القاحلة المحيطة بالجانب الجنوبي من"
" منحدر البركان مع جولات ماوي المروحية."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "التحرير في الواجهة الخلفية "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "تحرير الـ '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "قم بتحرير ترويسة صفحة \"كل المدونات\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "قم بتحرير ترويسة صفحة \"نتائج التصفية\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "فيسبوك"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "حقائق يجب أن تضعها بعين الاعتبار. "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"أخيرًا والأهم من ذلك ، كان يجب أن يقوم فريق فحص دليل الفندق بزيارة الفندق "
"المعني بشكل منتظم ، ومقابلة الموظفين ، والنوم في غرفة نوم وتجربة الطعام. يجب"
" أن يجربوا الفندق كما يمكن لنزيل الفندق فقط وعندها فقط يكونون في وضع قوي "
"حقًا للكتابة عن الفندق."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Follow Us"
msgstr "تابعنا"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"بالنسبة للكثيرين منا ممن يسكنون المدن ، لا نلاحظ حقًا تلك السماء هناك بشكل "
"روتيني. تقوم أضواء المدينة بعمل جيد في إخفاء العرض المذهل الذي يعلو فوق "
"رؤوسنا جميعًا طوال الوقت."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr ""
"بالنسبة للكثيرين منا ، تبدأ تجربتنا الأولى للتعرف على الأجرام السماوية عندما"
" رأينا أول قمر مكتمل في السماء. إنه حقًا منظر رائع حتى بالعين المجردة."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"من أصغر طفل إلى عالم الفيزياء الفلكية الأكثر تقدمًا ، هناك شيء لأي شخص يرغب "
"في الاستمتاع بعلم الفلك. في الواقع ، إنه علم يسهل الوصول إليه بحيث يمكن لأي "
"شخص تقريبًا القيام به في أي مكان يوجد فيه. كل ما عليهم أن يعرفوه هو البحث."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "تعرف على شخص خبير "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "احصل على تلسكوب"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "احصل على بعض التاريخ"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "ابدأ"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "التجميع حسب "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "توجد رسالة "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr "فيما يلي بعض الحقائق الأساسية التي يجب أن تضعها في اعتبارك:"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "نصائح العطلة"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "كيف تبحث"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"ما مدى تعقيد التلسكوب وهل ستواجه مشكلة في الصيانة؟ شبكة للحصول على إجابات "
"لهذه الأسئلة وغيرها. إذا قمت بأداء واجبك المنزلي مثل هذا ، فستجد التلسكوب "
"المناسب لهذه الخطوة الكبيرة التالية في تطور شغفك بعلم الفلك."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "كيف يجب أن يكون التلسكوب المحمول الخاص بك؟"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
msgid "How to choose the right hotel"
msgstr "كيفية اختيار الفندق المناسب"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "المُعرف"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"إذا كان من المهم أن يكون فندقك ، على سبيل المثال ، على الشاطئ ، أو قريبًا من"
" المنتزه الترفيهي ، أو مناسبًا للمطار ، فإن الموقع يكون أمرًا بالغ الأهمية. "
"يجب أن يقدم أي دليل لائق خريطة موقع للفندق والمناطق المحيطة به. يجب أن تكون "
"هناك مخططات للمسافات إلى المطار معروضة بالإضافة إلى شكل من أشكال الخرائط "
"التفاعلية."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"إذا كان الليل صافًا ، يمكنك رؤية تفاصيل مذهلة لسطح القمر بمجرد التحديق بالنجوم في الفناء الخلفي الخاص بك.\n"
"بطبيعة الحال ، كلما زاد حبك لعلم الفلك ، ستجد العديد من الأجرام السماوية رائعة. لكن القمر قد يكون دائمًا حبنا الأول لأنه الجسم الفضائي البعيد الذي يتميز بميزة فريدة تتمثل في الطيران بالقرب من الأرض والذي سار عليه الإنسان."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
msgid "In"
msgstr "في"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"من نواح كثيرة ، إنها خطوة كبيرة من شخص يتلاعب بعلم الفلك إلى طالب جاد في "
"العلوم. لكنك وأنا نعلم أنه لا تزال هناك خطوة كبيرة أخرى بعد شراء التلسكوب "
"قبل أن تعرف حقًا كيفية استخدامه."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
msgid "Is Published"
msgstr "تم نشره "

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Islands"
msgstr "الجزر "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"إنه لأمر ممتع أن تبدأ في تعلم الأبراج وكيفية التنقل في سماء الليل والعثور "
"على الكواكب والنجوم الشهيرة. هناك مواقع ويب وكتب وافرة لإرشادك."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"من المهم اختيار فندق يجعلك تشعر بالراحة - أثاثات معاصرة أو تقليدية ، ديكور "
"محلي أو دولي ، رسمي أو مريح. يجب أن يعلمك دليل الفندق المثالي بالخيارات "
"المتاحة."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr ""
"من الآمن أن نقول إنه في مرحلة ما من حياتنا ، يمر كل واحد منا بتلك اللحظة "
"عندما نشعر بالذهول فجأة عندما نواجه ضخامة الكون التي نراها في سماء الليل "
"وجهاً لوجه."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"إنه لأمر مدهش حقًا عندما تفكر في أنه بمجرد النظر في أي ليلة معينة ، يمكنك أن"
" ترى تقريبًا مئات الآلاف من النجوم وأنظمة النجوم والكواكب والأقمار "
"والكويكبات والمذنبات وربما حتى مكوك فضائي عرضي . سيكون الأمر أكثر إثارة "
"عندما تدرك أن السماء التي تنظر إليها هي لجميع المقاصد والأغراض نفس السماء "
"التي استمتع بها أسلافنا منذ مئات وآلاف السنين عندما نظروا للتو."

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Jungle"
msgstr "الأدغال "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "اعرف ما الذي تبحث عنه"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "تعرف متى تنظر"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Large"
msgstr "كبير"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "المشارك الأخير"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Latest"
msgstr "الأحدث"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_latest_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "أحدث منشورات المدونة "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"إن تعلم خلفية الاكتشافات العظيمة في علم الفلك سيجعل لحظاتك في التحديق "
"بالنجوم ذات مغزى أكبر. إنه أحد أقدم العلوم على وجه الأرض ، لذا اكتشف عظماء "
"التاريخ الذين نظروا إلى هذه النجوم قبلك."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "المرافق الترفيهية"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"اللون المحلي رائع ولكن المطاعم والبارات الخاصة بالفندق يمكن أن تلعب دورًا "
"مهمًا في إقامتك. يجب أن تكون على دراية بالاختيار والأسلوب وما إذا كانت ذكية "
"أو غير رسمية أم لا. يجب أن يخبرك تقرير الفندق الجيد بهذا ، وخاصة عن مرافق "
"الإفطار."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "الموقع "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_main_attachment_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "مارلي"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Maui helicopter tours"
msgstr "جولات ماوي المروحية"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"تعد جولات ماوي المروحية طريقة رائعة لمشاهدة الجزيرة من منظور مختلف "
"والاستمتاع بمغامرة ممتعة. إذا لم تكن على متن طائرة هليكوبتر من قبل ، فهذا "
"مكان رائع للقيام بذلك."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"تعد جولات ماوي المروحية طريقة رائعة للقيام بجولة في تلك الأماكن التي لا يمكن"
" الوصول إليها سيرًا على الأقدام أو بالسيارة. تستغرق الجولات حوالي ساعة واحدة"
" وتتراوح ما بين مائة وثمانية وخمسة دولارات إلى مائتين وأربعين دولارًا للفرد."
" بالنسبة للكثيرين ، هذه فرصة العمر لرؤية مناظر طبيعية لن تكون متاحة مرة "
"أخرى. سيسمح لك التقاط الكاميرات ومقاطع الفيديو لالتقاط اللحظات بإعادة إحياء "
"الجولة مرارًا وتكرارًا كما تتذكر على مر السنين."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"ستسمح لك جولات ماوي المروحية بمشاهدة كل هذه المعالم. تأكد من اصطحاب كاميرا "
"أو مقطع فيديو معك عند الذهاب في جولات مروحية ماوي لالتقاط جمال المشهد وإظهار"
" الأصدقاء والعائلة في المنزل كل الأشياء الرائعة التي شاهدتها أثناء الإجازة."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Medium"
msgstr "وسط"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "الوصف الدلالي "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "الكلمات المفتاحية الوصفية "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "العنوان الوصفي "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "مولوكاي ماوي"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"ستأخذك جولات ملوكي ماوي المروحية إلى جزيرة مختلفة ولكن واحدة على بعد تسعة أميال فقط ويمكن الوصول إليها بسهولة عن طريق الجو. تحتوي هذه الجزيرة على عدد قليل جدًا من السكان لديهم ثقافة ومناظر طبيعية مختلفة. الساحل الشمالي الشرقي بأكمله تصطف على جانبيه المنحدرات والشواطئ النائية. لا يمكن الوصول إليها تمامًا بأي وسيلة نقل أخرى غير الجو.\n"
"الأشخاص الذين يعيشون على الجزيرة لم يروا هذا المشهد الرائع أبدًا إلا إذا قاموا بجولات بطائرة هليكوبتر ماوي لمشاهدته. عندما يكون الطقس ممطرًا وتهطل الأمطار بغزارة في موسمه ، سترى العديد من الشلالات المذهلة."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"والأهم من ذلك بالنسبة للمسافر العائلي من مسافر العمل ، يجب أن تكتشف مدى "
"ملاءمة الفندق للأطفال من الدليل واتخاذ قرارك من هناك. شيء واحد يستحق البحث "
"عنه هو ما إذا كان الفندق يقدم خدمة جليسات الأطفال. بالنسبة للمسافرين من رجال"
" الأعمال الذين يرغبون في الهروب من الأطفال ، يعد هذا بالطبع وثيق الصلة أيضًا"
" - ربما يكون الفندق غير الملائم للأطفال شيئًا أكثر ملاءمة!"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_most_viewed_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "منشورات المدونة الأكثر عرضاً "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "الاسم"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "New Blog Post"
msgstr "منشور مدونة جديد "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "لا توجد منشورات في المدونة بعد. "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "عدد المشاهدات"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr "لا توجد نتائج لـ \"%s\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "لم يتم العثور على نتائج لـ’"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "لم يتم تحديد أي علامات تصنيف بعد. "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "لا شيء"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"لا يمنعك أي من هذا من المضي قدمًا في خططك لتكوين نظام تلسكوب رائع. فقط تأكد "
"من حصولك على مشورة الجودة والتدريب على كيفية تكوين التلسكوب الخاص بك لتلبية "
"احتياجاتك. باستخدام هذه الإرشادات ، ستستمتع بساعات من الاستمتاع بالنجوم في "
"المشاهد الرائعة في سماء الليل التي تتجاوز العين المجردة."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"لن تضمن لك معرفة الطقس أن تحديقك في النجوم أمر مجدٍ فحسب، بل ستعلم أيضاً متى"
" ستقع زخات النيازك الكبيرة وغيرها من الأحداث الكبيرة في علم الفلك، وستجعلك "
"إثارة علم الفلك تنبض بالحياة. "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"بالطبع، للاستمتاع بمراقبة القمر إلى أقصى حد، فإن رفع أجهزتك إلى تلسكوب بداية"
" جيدة سيمكّنك من رؤية أروع تفاصيل سطح القمر. مع كل من هذه الترقيات، ستتحسن "
"معرفتك وعمق ونطاق ما ستتمكن من رؤيته هندسيًا. بالنسبة للعديد من علماء الفلك "
"الهواة، لا يمكننا أحيانًا الحصول على ما يكفي مما يمكننا رؤيته على أقرب جسم "
"فضائي."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Once you have reviewed the content on mobile, close the preview."
msgstr "بمجرد أن تراجع المحتوى على الهاتف المحمول، أغلق شاشة المعاينة."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "آخرون"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "مدوناتنا"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "تصوير أنطون ريبونين ، @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "تصوير آرتو مارتنين ، @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "تصوير بوريس سموكروفيتش ، @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "تصوير دينيس نيفوزاي ، @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr "تصوير جريج راكوزي ، @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "تصوير جيسون بريسكو ، @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "تصوير جون لي ، @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "تصوير باتريك برينكسما ، @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "الصورة بواسطة بولو أكس هينانديز ، @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "الصورة بواسطة سبيس أكس ، @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "تصوير تيدي كيلي ، @teddykelley"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "المنشورات "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "تاريخ النشر "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "تم النشر ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "تاريخ النشر"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "المنشور الذي تمت مشاركته "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "خيارات النشر"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "تاريخ النشر"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Read more"
msgstr "قراءة المزيد"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"fa fa-chevron-right ml-2\"/>"
msgstr "قراءة المزيد <i class=\"fa fa-chevron-left ml-2\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "مطاعم ومقاهي وحانات "

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict publishing to this website."
msgstr "قصر إمكانية النشر على هذا الموقع."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "تحسين محركات البحث "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "تم تحسين محركات البحث"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "عينة"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Satellites"
msgstr "الأقمار الصناعية "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Search for an image. (eg: type \"business\")"
msgstr "ابحث عن صورة. (على سبيل المثال: اكتب \"أعمال\")"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seaside vs mountain side"
msgstr "بجانب البحر أو بجانب الجبال "

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seeing the world from above"
msgstr "رؤية العالم من الأعالي "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "Select Blog"
msgstr "اختر المدونة"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select the blog you want to add the post to."
msgstr "حدد المدونة التي تريد إضافة المنشور إليها."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr "اختر هذا العنصر من القائمة لإنشاء منشور مدونة جديد. "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "اسم محسنات محرك البحث "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "افصل بين كل كلمة مفتاحية بفاصلة "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Set a blog post <b>cover</b>."
msgstr "تعيين <b>غلاف</b> لمنشور المدونة. "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"العديد من الطيور والثدييات والزواحف المهاجرة والمحلية تسمى كوبر كانيون "
"موطنها. الحيوانات الرائعة في هذه الأرض القريبة من البِكر تستحق الزيارة "
"أيضًا."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "المشاركة على فيسبوك"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "المشاركة على LinkedIn"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Twitter"
msgstr "المشاركة على تويتر"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "شارك هذا المنشور"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
msgid "Sierra Tarahumara"
msgstr "سييرا تاراهومارا"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr ""
"تقع سييرا تاراهومارا ، المعروفة شعبياً باسم كوبر كانيون ، في المكسيك. تعد "
"المنطقة وجهة مفضلة لمن يبحثون عن إجازة مليئة بالمغامرات."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Silly-Chico"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Skies"
msgstr "السماء "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"لذلك من المهم للغاية أن تحصل على التلسكوب المناسب لمكان وجودك وما هي "
"تفضيلاتك في التحديق بالنجوم. في البداية ، دعنا نناقش الأنواع الثلاثة "
"الرئيسية للتلسكوبات ثم نضع بعض مفاهيم \"التلسكوبات الاساسية\" لزيادة فرصك في"
" شراء الشيء الصحيح."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"لذلك قد تكون إجازة مرة واحدة في العام إلى مكان تخييم أو رحلة إلى منزل أحد "
"الأقارب في الخارج عندما نجد أنفسنا في الخارج عندما يقرر منفق السماء ليلاً "
"فجأة أن يقدم عرضًا مذهلاً. إذا مررت بهذا النوع من اللحظات عندما صدمت حرفيًا "
"أنفاسك من قبل المنفق الذي يمكن أن تظهره لنا سماء الليل ، يمكنك على الأرجح أن"
" تتذكر تلك اللحظة بالتحديد عندما لا يمكنك أن تقول شيئًا آخر ولكن \"رائع\" "
"لما رأيته."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"لذا ، لاختيار النوع المناسب من التلسكوب ، فإن أهدافك في استخدام التلسكوب "
"مهمة. لفهم نقاط القوة والضعف ليس فقط في تصميم العدسات والتلسكوب ولكن أيضًا "
"في كيفية أداء التلسكوب في المواقف المختلفة لتحديق النجوم ، فمن الأفضل القيام"
" ببعض الواجبات المنزلية مقدمًا والتعرف على الأنواع المختلفة. لذلك قبل إجراء "
"عملية الشراء الأولى..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr "إذا كنت مسافرًا إلى الخارج، واخترت وجهتك وعليك الآن اختيار فندق. "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr "أحدهم مشهور في <cite title=\"العنوان المصدر \">العنوان المصدر</cite> "

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Spotting the fauna"
msgstr "رؤية الحيوانات البرية "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/models/website_blog.py:0
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Start writing here..."
msgstr "ابدأ الكتابة من هنا..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "النمط"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "العنوان الفرعي"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "العنوان الفرعي"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "فئات علامات التصنيف "

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "فئة علامة التصنيف "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr "استمارة فئة علامة التصنيف "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "استمارة علامة التصنيف "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "قائمة علامات التصنيف "

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists !"
msgstr "فئة علامة التصنيف موجودة بالفعل! "

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists !"
msgstr "اسم علامة التصنيف موجود بالفعل! "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "علامات التصنيف "

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Taking pictures in the dark"
msgstr "التقاط الصور في الظلام "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
msgid "Teaser"
msgstr "العرض التشويقي"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "محتوى العرض التشويقي"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"قبل عشر سنوات ، ربما تكون قد زرت وكيل سفرك المحلي ووثقت في النصيحة وجهًا "
"لوجه التي قدمها لك ما يسمى \"الخبراء\". طريقة القرن الحادي والعشرين لاختيار "
"فندقك وحجزه هي بالطبع على الإنترنت ، باستخدام مواقع السفر."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"تلك اللحظة \"المدهشة\" هي ما يدور حوله علم التنجيم. بالنسبة للبعض ، تصبح تلك"
" اللحظة المبهرة شغفًا يؤدي إلى مهنة تدرس النجوم. بالنسبة للقلة المحظوظة ، "
"هذه اللحظة المبهرة لأن هوسًا مستهلكًا يقودهم إلى السفر إلى النجوم في مكوك "
"الفضاء أو في إحدى مهماتنا الفضائية المبكرة. لكن بالنسبة لمعظمنا ، قد يصبح "
"علم التنجيم هواية أو هواية عادية. لكننا نحمل هذه اللحظة المبهرة معنا لبقية "
"حياتنا ونبدأ في البحث عن طرق للبحث بشكل أعمق ومعرفة المزيد عن الكون المذهل "
"الذي نراه في ملايين النجوم فوقنا كل ليلة."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr "جمال علم الفلك هو أن أي شخص يمكنه فعل ذلك."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"من الواضح أن أفضل وقت لمشاهدة القمر هو في الليل عندما يكون هناك القليل من "
"السحب ويكون الطقس مناسبًا لدراسة طويلة ودائمة. ينتج الربع الأول أكبر تفاصيل "
"الدراسة. ولا تنخدعوا إلا بمسح جزء من القمر عندما لا يكون في مرحلة اكتمال "
"القمر. تمنحك الظاهرة المعروفة باسم \"سطوع الأرض\" القدرة على رؤية الجزء "
"المظلم من القمر ببعض التفاصيل أيضًا ، حتى لو كان القمر في عرض ربع أو نصف "
"فقط."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "أفضل وقت لمشاهدة القمر."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr ""
"سوف يكون منشور المدونة ظاهرًا لزوارك على الموقع الإلكتروني ابتداءً من هذا "
"التاريخ إذا تم تعيينه كمنشور تم نشره. "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"المنحدرات في هذه المنطقة هي من بين أعلى المنحدرات في العالم ورؤية المياه "
"تتدفق من القمم العالية هو ببساطة مذهل. رحلة قصيرة من Maui مع جولات هليكوبتر "
"Maui تستحق رؤية جمال هذه البيئة الطبيعية."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr "الرابط الكامل للوصول للمستند من خلال الموقع."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"الشيء التالي الذي نريد بطبيعة الحال الحصول عليه هو تلسكوب جيد. ربما تكون قد "
"رأيت أحد الهواة الذي كان جيدًا في دراستهم يقوم بإعداد تلك التلسكوبات الرائعة"
" حقًا على تل في مكان ما. هذا يثير عالم الفلك الهاوي بداخلك لأن ذلك يجب أن "
"يكون الخطوة المنطقية التالية في نمو هوايتك. لكن كيفية شراء تلسكوب جيد يمكن "
"أن تكون مربكة ومخيفة بصراحة."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"يجب أن يقدم الموقع تحليلاً مفصلاً للخدمات الترفيهية داخل الفندق - منتجع صحي "
"، مسبح ، صالة ألعاب رياضية ، ساونا - بالإضافة إلى تفاصيل أي مرافق أخرى قريبة"
" مثل ملاعب الجولف. 7. الاحتياجات الخاصة: يجب أن يقوم موقع دليل الفنادق "
"بإبلاغ الزائر بخدمات ذوي الاحتياجات الخاصة وسياسة الوصول الخاصة بكل فندق. في"
" حين أن هذا لا ينطبق مرة أخرى على كل زائر ، إلا أنه أمر مهم للغاية بالنسبة "
"للبعض. "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr ""
"سيتغير الحامل ثلاثي القوائم أو قرارات الملحقات الأخرى بشكل كبير باستخدام "
"التلسكوب الذي سيعيش على سطح السفينة الخاص بك مقابل التلسكوب الذي تخطط لنقله "
"إلى العديد من المواقع البعيدة."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"هذا المنظر مذهل حقًا وهو مشهد لا ينبغي تفويته. إنه أيضًا تعليمي للغاية مع "
"فرصة لرؤية بركان خامد عن قرب ، وهو شيء لا يمكن رؤيته كل يوم. على الجانبين "
"الشمالي والجنوبي من البركان ، سترى منظرًا مختلفًا لا يصدق. هذه الجوانب مورقة"
" وخضراء وستكون قادرًا على رؤية بعض الشلالات الجميلة والمناظر الخلابة. تكثر "
"الغابات الاستوائية المطيرة في هذا الجانب من الجزيرة ولا يمكن الوصول إليها "
"بسهولة بأي وسيلة أخرى غير الجو. "

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"ثم هناك مشكلة دافع المراجع. كلما قرأت المزيد من المراجعات ، كلما لاحظت كيف "
"تميل إلى التجمع على النقيض من الرأي. من ناحية ، لديك مراجعين غاضبين لديهم "
"محاور للطحن ؛ ومن ناحية أخرى ، فقد أسعدت الضيوف الذين يثنون على الثناء بشكل "
"لا يصدق. لن تندهش عندما تعلم أن الفنادق تنشر أحيانًا تقييماتها البراقة ، أو "
"يصطف هذا المنافس للحصول على فرصة لانتقاد المنافسة بالتعليقات السيئة. من "
"المنطقي مراعاة ما هو مهم حقًا بالنسبة لك عند اختيار فندق. يجب عليك بعد ذلك "
"اختيار دليل الفنادق عبر الإنترنت الذي يقدم معلومات حديثة ومستقلة ومحايدة "
"مهمة حقًا."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr "هناك اعتبارات أخرى يجب مراعاتها في قرار الشراء النهائي."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""
"هناك شيء خالد في الكون. حقيقة أن الكواكب والقمر والنجوم التي خلفها كانت "
"موجودة منذ زمن طويل تفعل شيئًا لإحساسنا بمكاننا في الكون. في الواقع ، العديد"
" من النجوم التي \"نراها\" بالعين المجردة هي في الواقع ضوء جاء من ذلك النجم "
"منذ مئات الآلاف من السنين. هذا الضوء وصل للتو إلى الأرض. وبطريقة حقيقية "
"للغاية ، فإن البحث يشبه السفر عبر الزمن."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""
"هذه الأشياء مهمة حقًا وأي دليل فندق لائق يجب أن يقدم لك هذا النوع من النصائح"
" حول غرف النوم - وليس فقط عدد الغرف وهو الخيار المعتاد!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr "لن يكون هذا المربع مرئيًا للزائرين"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "This tag already exists"
msgstr "علامة التصنيف هذه موجودة بالفعل "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Tiny"
msgstr "صغير"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "العنوان"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""
"عند النظر إلى القمر بالعين المجردة ، فإن التعرف على خريطة القمر سيساعدك على "
"التقاط البحار والحفر والظواهر الجغرافية الأخرى التي رسمها الآخرون بالفعل "
"لجعل دراستك أكثر إمتاعًا. يمكن الحصول على خرائط القمر من أي متجر فلك أو عبر "
"الإنترنت وهي تستحق الاستثمار."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""
"للبدء في تعلم كيفية مراقبة النجوم بشكل أفضل بكثير ، هناك بعض الأشياء "
"الأساسية التي قد نحتاج إلى النظر فيها بشكل أعمق ، بخلاف ما يمكننا رؤيته "
"بالعين المجردة والبدء في دراسة النجوم والاستمتاع بها. أول شيء تحتاجه ليس "
"المعدات على الإطلاق بل الأدب. ستُظهر لك خريطة النجوم الجيدة الأبراج الرئيسية"
" وموقع النجوم الرئيسية التي نستخدمها للتنقل في السماء والكواكب التي ستظهر "
"أكبر من النجوم. وإذا أضفت إلى تلك الخريطة بعض المواد التمهيدية التي تم "
"إجراؤها جيدًا في هواية علم الفلك ، فأنت على الطريق الصحيح."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""
"لرفعه قليلاً ، يمكن لزوج جيد من المناظير أن يفعل العجائب للحصول على التفاصيل"
" التي ستراها على سطح القمر. للحصول على أفضل النتائج ، احصل على مجال واسع جيد"
" في إعدادات مجهر حتى تتمكن من الاستمتاع بالمناظر الطبيعية للقمر بكل جمالها. "
"ولأنه يكاد يكون من المستحيل الإبقاء على المنظار ثابتًا طوال الوقت الذي تريد "
"أن تنظر فيه إلى هذا الجسم الرائع في الفضاء ، فقد ترغب في إضافة حامل ثلاثي "
"القوائم جيد إلى ترسانة المعدات الخاصة بك بحيث يمكنك تثبيت المنظار عليه حتى "
"تتمكن من ذلك. دراسة القمر براحة ومن خلال منصة عرض مستقرة."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""
"للانتقال به إلى المستوى التالي الطبيعي ، قد ترغب في الاستفادة من الشراكات مع"
" علماء فلك آخرين أو من خلال زيارة أحد التلسكوبات العظيمة حقًا التي تم "
"إنشاؤها من قبل المتخصصين الذين استثمروا في تقنيات أفضل للقضاء على التداخل في"
" الغلاف الجوي لرؤية القمر حتى أفضل. يمكن أن يمنحك الإنترنت إمكانية الوصول "
"إلى هابل والعديد من التلسكوبات الضخمة الموجهة نحو القمر طوال الوقت. علاوة "
"على ذلك ، تعمل العديد من نوادي علم الفلك على طرق لدمج تلسكوبات متعددة ، "
"متزامنة بعناية مع أجهزة الكمبيوتر للحصول على أفضل رؤية للمناظر الطبيعية "
"للقمر."

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr "السفر"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "تويتر"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "غير منشور ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل غير المقروءة "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "منشور بلا عنوان "

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr ""
"استخدم هذه الأيقونة لمعاينة كيف يبدو منشور مدونتك على <b>أجهزة الهاتف "
"المحمول</b>."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "مستخدم في:"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Viewpoints"
msgstr "نقاط العرض "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "أدوات العرض"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr "مرئي في كافة صفحات المدونات "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "مرئي في الموقع الإلكتروني الحالي "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "WRITE HERE OR DRAG BUILDING BLOCKS"
msgstr "اكتب هنا أو قم بسحب الكتل الإنشائية "

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "الموقع الإلكتروني"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "مدونات الموقع الإلكتروني "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "عامل تصفية قصاصات الموقع الإلكتروني "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "رابط الموقع الإلكتروني "

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "الوصف الدلالي في الموقع الإلكتروني "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "الكلمات الدلالية في الموقع الإلكتروني "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "العنوان الدلالي في الموقع الإلكتروني "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "صورة الرسم البياني المفتوح للموقع الإلكتروني "

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr "ماذا لو سمحوا لك بتشغيل تليسكوب هابل"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""
"بينما يمكن لأي شخص البحث عن النجوم والوقوع في حبها في أي وقت ، فإن متعة علم "
"الفلك هي تعلم كيفية أن تصبح أكثر وأكثر مهارة ومجهزة في التحديق بالنجوم الذي "
"تراه وتفهمه أكثر فأكثر في كل مرة تنظر فيها. فيما يلي بعض الخطوات التي يمكنك "
"اتخاذها لجعل اللحظات التي يمكنك تخصيصها لهواية علم الفلك أكثر إمتاعًا."

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "With a View"
msgstr "مع إطلالة "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr "اكتب نصًا صغيرًا هنا لوصف مدونتك أو شركتك."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Write a title, the subtitle is optional."
msgstr "اكتب عنوانًا، العنوان الفرعي اختياري."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""
"يجب عليك دائمًا التفكير بعناية في نوع المرافق التي تحتاجها من غرفة نومك "
"والعثور على الفندق الذي به تلك التي تعتبرها مهمة. يجب أن يوضح موقع دليل "
"الفندق على الويب أمورًا مثل: حجم السرير ، والوصول إلى الإنترنت (تكلفته ، "
"سواء كان هناك اتصال واي فاي أو اتصال سلكي واسع النطاق) ، ووسائل الراحة "
"المجانية ، وإطلالات من الغرفة والعروض الفاخرة مثل قائمة الوسائد أو قائمة باث"
" ، واختيار من غرف للمدخنين أو لغير المدخنين وما إلى ذلك."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""
"سترى كل الجمال الذي تقدمه ماوي ويمكن أن تقضي وقتًا ممتعًا لجميع أفراد "
"الأسرة. الجولات ليست باهظة الثمن وتستمر من خمس وأربعين دقيقة إلى أكثر من "
"ساعة. يمكنك مشاهدة الأماكن التي يتعذر الوصول إليها عادةً من خلال جولات "
"طائرات الهليكوبتر في ماوي. يمكن رؤية الأماكن غير المتوفرة سيرًا على الأقدام "
"أو بالسيارة عن طريق الجو. مناظر خلابة تنتظر أولئك الذين يستعدون لبعض جولات "
"مروحية ماوي الممتعة. إذا كنت ستقيم في الجزيرة لفترة طويلة من الوقت ، فقد "
"ترغب في التفكير في القيام بجولات متعددة بطائرة هليكوبتر ماوي."

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr "مغامرة "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr "مدونة. اضغط هنا للوصول للمدونة:"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr "التتبع "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "بواسطة"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr "اكتشاف"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr "إرشادات"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "تم نشره على"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr "فنادق"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "في"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "in <i class=\"fa fa-folder-open text-muted\"/>"
msgstr "في <i class=\"fa fa-folder-open text-muted\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "in <i class=\"fa fa-folder-open text-white-75\"/>"
msgstr "في <i class=\"fa fa-folder-open text-white-75\"/>"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr "التلسكوبات"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr "حتى تترك تعليقاً "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "غير منشور"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted mr-1\"/>"
msgstr ""
"|\n"
"                            <i class=\"fa fa-comment text-muted mr-1\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr "لا توجد تعليقات بعد "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "⌙ Hover effect"
msgstr "⌙ تأثير التمرير "
