# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# a75f12d3d37ea5bf159c4b3e85eb30e7_0fa6927, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-14 15:42+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "#{year}"
msgstr "#{year}"

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (cópia)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr "' cabeçalho da página."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr "'. Mostrando resultados para '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr "-- Todas as datas"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""
"<b>Os binóculos são leves e portáteis</b> A menos que você tenha o luxo de "
"montar e operar um observatório em seu deck, provavelmente irá viajar para "
"realizar suas visualizações. Binóculos vão com você muito mais facilmente e "
"são mais leves para transportar para o país e usar enquanto você estiver lá "
"do que um kit de montagem de telescópio pesado"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Click on Save</b> to record your changes."
msgstr "<b>Clique em Salvar</b> para armazenar suas alterações."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""
"<b>Escolha os cérebros dos especialistas</b>. Se você ainda não participa de"
" uma sociedade ou clube de astronomia, os vendedores da loja de telescópios "
"poderão orientá-lo sobre as sociedades ativas em sua área. Depois de "
"estabelecer conexões com pessoas que compraram telescópios, você pode obter "
"conselhos sobre o que funciona e o que evitar, o que é mais válido do que "
"qualquer coisa que você obterá de um artigo da web ou de um vendedor do Wal-"
"Mart."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr "<b>Publique seu post</b> para torná-lo visível aos seus visitantes."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>Entrar</b>"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>That's it, your blog post is published!</b> Discover more features "
"through the <i>Customize</i> menu."
msgstr ""
"<b>É isso aí, seu post foi publicado!</b> Descubra mais recursos através do "
"menu de <i>Personalização</i>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""
"<b>Teste antes de comprar.</b> Essa é outra vantagem de fazer algumas "
"viagens de campo com o clube de astronomia. Você pode reservar algumas horas"
" de qualidade com pessoas que conhecem telescópios e ter suas plataformas "
"preparadas para examinar seus equipamentos, aprender os principais aspectos "
"técnicos e testá-los antes de gastar dinheiro em sua própria configuração."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>Escreva sua história aqui.</b> Use a barra de ferramentas superior para "
"estilizar seu texto: adicione uma imagem ou tabela, defina negrito ou "
"itálico, etc. Arraste e solte blocos de construção para mais blogs gráficos."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr ""
"<em class=\"h4 my-0\">Além da população nativa, a vida selvagem local também"
" atrai multidões.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr ""
"<em class=\"h4 my-0\">É extremamente importante que você obtenha o "
"telescópio certo.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr ""
"<em class=\"h4 my-0\">Aquele momento “Wow” é a essência da astrologia.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr ""
"<em class=\"h4 my-0\">Quanto mais comentários você lê, mais você percebe "
"como eles tendem a se agrupar nos extremos de opinião.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">Há algo atemporal no cosmos.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr ""
"<em class=\"h4 my-0\">Seu estudo da lua, como qualquer outra coisa, pode ir "
"do simples ao muito complexo.</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>Nenhum marcador definido</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"Para o conteúdo "
"do blog\" title=\"Para o conteúdo do blog\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Post date\" title=\"Post date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Data do post\" "
"title=\"Data do post\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-sm\">Read "
"Next</span>"
msgstr ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-sm\">Ler "
"Próximo</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"mr-1\">Show:</span>"
msgstr "<span class=\"mr-1\">Exibir:</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled pl-0\">Blogs:</span>"
msgstr "<span class=\"nav-link disabled pl-0\">Blogs:</span>"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "Uma ótima maneira de descobrir lugares escondidos"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr ""
"Umas férias no Copper Canyon prometem ser uma excitante mistura de "
"relaxamento, cultura, história, vida selvagem e caminhadas."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "Novo post"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"O viajante pode optar por explorar a área caminhando ao redor do cânion ou "
"aventurando-se nele. Um planejamento detalhado é necessário para aqueles que"
" desejam se aventurar nas profundezas do cânion. Existem várias agências de "
"viagens especializadas na organização de excursões à região. Os visitantes "
"podem voar para Copper Canyon usando um visto de turista, que é válido por "
"180 dias. Os viajantes também podem dirigir de qualquer lugar dos Estados "
"Unidos e adquirir um visto na alfândega mexicana na fronteira."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "Sobre nós"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
"Acima de tudo, <b>estabelecer um relacionamento com uma loja de telescópios "
"de boa reputação</b> que emprega pessoas que conhecem suas coisas. Se você "
"comprar seu telescópio em um Wal-Mart ou loja de departamentos, as chances "
"de obter a coisa certa são remotas."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "Post de acesso"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "Ação Necessária"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "Ativo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "Adicionar algo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "Tudo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "Todos os Blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "Todos os blogs"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Alone in the ocean"
msgstr "Solitário"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "Nesse sentido, quão difícil é a configuração e a desmontagem?"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.js:0
#, python-format
msgid "Amazing blog article: %s! Check it live: %s"
msgstr "Artigo impressionante do blog: %s! Confira ao vivo: %s"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr ""
"Uma emocionante mistura de relaxamento, cultura, história, vida selvagem e "
"caminhadas."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"E quando tudo estiver dito e feito, <b>prepare-se</b>. Sua busca por "
"telescópios mais novos e melhores será para toda a vida. Fique viciado em "
"astronomia e a experiência irá enriquecer todos os aspectos da sua vida. "
"Será um vício que você nunca vai querer quebrar."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"Outro recurso único da Copper Canyon é a presença da cultura indígena "
"Tarahumara. Essas pessoas semi-nômades vivem em cavernas. Seu sustento "
"depende principalmente da agricultura e da pecuária."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
msgid "Archive"
msgstr "Arquivo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "Arquivado"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
msgid "Archives"
msgstr "Arquivos"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "Artigo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "Artigos"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "Astronomia"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"Os clubes de astronomia são lugares animados, cheios de amadores experientes"
" que adoram compartilhar seus conhecimentos com você. Pelo preço de uma coca"
" e lanches, eles vão olhar as estrelas com você e sobrecarregá-lo com "
"curiosidades e grande conhecimento."

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "A Astronomia é “observar estrelas\""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Feed Atom"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "Contagem de Anexos"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Autor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "Nome do autor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "Avatar"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Awesome hotel rooms"
msgstr "Quartos de hotel incríveis"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr "Esteja ciente dessa coisa chamada “astronomia”"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"Tornar-se parte da sociedade de devotos astrônomos amadores lhe dará acesso "
"a esses esforços organizados para alcançar novos níveis em nossa capacidade "
"de estudar a lua da Terra. E ele lhe dará colegas e amigos que compartilham "
"sua paixão pela astronomia e que podem compartilhar suas experiências e "
"áreas de especialização enquanto você procura encontrar onde você pode olhar"
" a seguir no imenso céu noturno, na lua e além dela em sua busca por "
"conhecimento sobre o universo aparentemente infinito acima de nós."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "Tornando-se parte da sociedade de devotados astrônomos amadores."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "Instalações de Quarto"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"Antes de partir para o próximo gasto, pode ser interessante investir em um "
"bom binóculo. Até têm binóculos que servem para observar as estrelas e que "
"te dão aquela visão extra para ver um pouco melhor as maravilhas do "
"universo. Um binóculo bem projetado também dá mais mobilidade e habilidade "
"para manter a sua “visão aprimorada” na ponta dos dedos quando esta visão "
"incrível se mostrar à você."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "Antes de fazer sua primeira compra..."

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "Por Trás do Olho"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog"
msgstr "Blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "Nome do Blog"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog Post"
msgstr "Post no Blog"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to be calling this file !"
msgstr "O Post <b>%s</b>parece estar chamando esse arquivo! "

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to have a link to this page !"
msgstr "A publicação <b>%s</b> parece ter um link para esta página !"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "Capa do Post de Blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "Título do post"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.actions.act_window,name:website_blog.action_blog_post
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
#, python-format
msgid "Blog Posts"
msgstr "Posts de Blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "Blog Legenda"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "Marcador do Blog"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "Categoria do Marcador de Blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "Marcadores do Blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "Título do Blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "Títulos do Blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr "Blog"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"Mas como você pode analisar as incríveis opções oferecidas? E, o que é mais "
"importante, você realmente confia nas fotografias e descrições dos hotéis "
"que eles mesmos concederam com a motivação de conseguir reservas? As "
"avaliações dos viajantes podem ser úteis, mas você precisa ter cuidado. Elas"
" costumam ser tendenciosas, às vezes desatualizadas e podem não servir aos "
"seus interesses. Como saber se os recursos que são importantes para o "
"avaliador são importantes para você?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "Comprando um Telescópio"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr ""
"Comprar o telescópio certo para levar seu amor pela astronomia ao próximo "
"nível é um grande próximo passo no desenvolvimento de sua paixão pelas "
"estrelas."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "Pode Publicar"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "Categoria"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "Instalações infantis"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Choose an image from the library."
msgstr "Escolha uma Imagem, da biblioteca."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Click here to add new content to your website."
msgstr "Clique aqui para adicionar novo conteúdo ao seu site."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr ""
"Clique em \"<b>Novo</b>\" no canto superior direito para escrever seu "
"primeiro post de blog."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "Fechar"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "Comentário"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Comments"
msgstr "Comentários"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "Conteúdo"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"O Copper Canyon é um dos seis desfiladeiros da região. Embora o nome sugira "
"que o desfiladeiro possa ter alguma relevância para a mineração de cobre, "
"esse não é o caso. O nome é derivado do cobre e do líquen verde que cobrem o"
" cânion. O Copper Canyon tem duas zonas climáticas. A região apresenta um "
"clima alpino no topo e um clima subtropical nos níveis mais baixos. Os "
"invernos são frios, com frequentes tempestades de neve nas altitudes mais "
"elevadas. Os verões são secos e quentes. A capital, Chihuahua, é um deserto "
"de alta altitude onde o clima varia de invernos frios a verões quentes. A "
"região é única devido aos vários ecossistemas que existem nela."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "Propriedades da Capa"

#. module: website_blog
#: model_terms:ir.actions.act_window,help:website_blog.action_blog_post
msgid "Create a new blog post"
msgstr "Crie uma nova postagem no blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "Criado em"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Data (novo a antigo)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Data (antigo a novo)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "Descrição"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "Dexter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_edit_options
msgid "Duplicate"
msgstr "Duplicar"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "East Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"Os passeios de helicóptero no leste de Maui lhe proporcionarão uma vista do "
"vulcão de 3.000 metros, o Haleakala ou Casa do Sol. Esse vulcão está inativo"
" e entrou em erupção pela última vez em 1790. Você poderá ver a cratera do "
"vulcão e a terra seca e árida que circunda o lado sul da encosta do vulcão "
"com os passeios de helicóptero em Maui."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "Editar no backend"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "Editar o '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "Editar o cabeçalho da página 'Todos os Blogs'."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "Editar o cabeçalho da página 'Resultados do Filtro'."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "Facebook"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "Fatos que você deve ter em mente."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"Por fim, e mais importante, a equipe de inspeção do diretório de hotéis de "
"qualidade deveria ter visitado o hotel em questão regularmente, conhecido os"
" funcionários, dormido em um quarto e experimentado a comida. Eles devem "
"vivenciar o hotel como somente um hóspede pode vivenciar, e só então estarão"
" realmente em uma posição forte para escrever sobre o hotel."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Follow Us"
msgstr "Siga-nos"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Parceiros)"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"Muitos de nós, que moramos em cidades, geralmente não percebem o céu lá em "
"cima. As luzes da cidade disfarçam bem a incrível exibição que está acima "
"das nossas cabeças o tempo todo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr ""
"Para muitos de nós, nossa primeira experiência de aprendizado sobre os "
"corpos celestes começou quando vimos nossa primeira lua cheia no céu. É "
"realmente uma visão magnífica, mesmo a olho nu."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"Desde crianças até o astrofísico mais avançado, há algo para qualquer pessoa"
" que queira saber mais sobre astronomia. Na verdade, é uma ciência tão "
"acessível que praticamente qualquer pessoa pode praticá-la em qualquer "
"lugar. É só olhar para cima."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "Get a Geek"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "Obtenha um telescópio"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "Saiba mais sobre a história"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "Começar"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Agrupar Por"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "Análise de Previsão:"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr "Aqui estão alguns dos fatos chave que você deve ter em mente:"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "Dicas de feriado"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "Como olhar para cima"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"Qual é a complexidade do telescópio e, quais problemas teria com a "
"manutenção? Converse com pessoas da área para obter as respostas a essas e "
"outras perguntas. Se fizer a lição de casa assim, você encontrará o "
"telescópio certo para o próximo grande passo na evolução de sua paixão pela "
"astronomia."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "O quão móvel o telescópio deve ser?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
msgid "How to choose the right hotel"
msgstr "Como escolher o hotel certo"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread
msgid "If checked, new messages require your attention."
msgstr "Se marcado, novas mensagens solicitarão sua atenção."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se marcado, algumas mensagens tem erro de entrega."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"Se for importante que seu hotel esteja, por exemplo, na praia, perto de um "
"parque temático ou no caminho do aeroporto, a localização é fundamental. "
"Qualquer diretório decente deve oferecer um mapa de localização do hotel e "
"de seus arredores. Deve haver gráficos de distância até o aeroporto, bem "
"como alguma forma de mapa interativo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"Se o céu estiver limpo, você poderá ver detalhes incríveis da superfície lunar apenas observando as estrelas em seu quintal.\n"
"Naturalmente, conforme o seu amor pela astronomia vai evoluindo, você achará muitos corpos celestes fascinantes. Mas a Lua talvez seja sempre o nosso primeiro amor, pois é o único objeto espacial distante que tem a distinção única de voar perto da Terra e sobre o qual o homem já caminhou."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
msgid "In"
msgstr "Em"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"De muitas formas, é um grande passo ir de alguém que está apenas brincando "
"com a astronomia a alguém que estuda a ciência seriamente. Mas você e eu "
"sabemos que ainda há outro grande passo após a compra de um telescópio, "
"antes de você realmente saber como usá-lo."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "É um seguidor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
msgid "Is Published"
msgstr "Está publicado"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Islands"
msgstr "Ilhas"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"É muito divertido começar a aprender sobre as constelações, como navegar no "
"céu noturno e encontrar os planetas e as famosas estrelas. Existem sites e "
"livros em abundância para guiá-lo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"É importante escolher um hotel que o faça sentir confortável – mobília "
"contemporânea ou tradicional, decoração local ou internacional, formal ou "
"descontraído. O diretório de hotéis ideal deve informá-lo sobre as opções "
"disponíveis."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr ""
"É seguro dizer que, em algum momento de nossas vidas, todos nós temos aquele"
" momento em que ficamos repentinamente atordoados quando nos deparamos com a"
" enormidade do universo que vemos no céu noturno."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"É muito incrível saber que, apenas olhando para cima em qualquer noite, você"
" pode ver centenas de milhares de estrelas, sistemas estelares, planetas, "
"luas, asteroides, cometas e talvez até mesmo um ônibus espacial ocasional "
"pode passar. É ainda mais impressionante quando você percebe que o céu para "
"o qual está olhando é, para todos os efeitos, exatamente o mesmo céu que "
"nossos ancestrais desfrutaram centenas e milhares de anos atrás quando "
"apenas olhavam para cima."

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Jungle"
msgstr "Selva"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "Saiba para o que está olhando"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "Saiba quando olhar"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Large"
msgstr "Largo"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "Último Contribuidor(a)"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Latest"
msgstr "Último"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_latest_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "Publicações recentes no blog"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"Aprender o contexto de grandes descobertas da astronomia tornará seus "
"momentos de contemplação das estrelas mais significativos. É uma das "
"ciências mais antigas da Terra, então descubra os grandes nomes da história "
"que observaram essas estrelas antes de você."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "Instalações de lazer"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"A cor do local é ótima, mas os restaurantes e bares do próprio hotel podem "
"desempenhar um papel importante na sua estadia. Você deve estar ciente da "
"escolha, estilo e se eles são elegantes ou informais. Um bom relatório de "
"hotel deve informar isso e, principalmente, sobre as instalações para café "
"da manhã."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "Local"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_main_attachment_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_main_attachment_id
msgid "Main Attachment"
msgstr "Anexo Principal"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "Marley"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Maui helicopter tours"
msgstr "Tour de helicóptero por Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"Os passeios de helicóptero em Maui são uma ótima maneira de ver a ilha de "
"uma perspectiva diferente e ter uma aventura divertida. Se você nunca esteve"
" em um helicóptero antes, este é um ótimo lugar para fazê-lo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"Os passeios de helicóptero em Maui são uma ótima maneira de visitar os "
"lugares que não podem ser alcançados a pé ou de carro. Os passeios duram "
"aproximadamente uma hora e variam de aproximadamente 185 a 240 dólares por "
"pessoa. Para muitos, esta é uma oportunidade única na vida de ver paisagens "
"naturais que não estarão disponíveis novamente. Levar câmeras e vídeos para "
"capturar os momentos também permitirá que você reviva o passeio várias vezes"
" enquanto relembra ao longo dos anos."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"Os passeios de helicóptero em Maui permitirão que você veja todos esses "
"pontos turísticos. Certifique-se de levar uma câmera ou filmadora com você "
"ao fazer passeios de helicóptero em Maui para capturar a beleza da paisagem "
"e mostrar aos amigos e familiares em casa todas as coisas maravilhosas que "
"você viu durante as férias."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Medium"
msgstr "Médio"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "Erro na entrega da Mensagem"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "Mensagens"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "Meta Description"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "Meta Keywords"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "Meta Title"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "Molokai Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"Os passeios de helicóptero Molokai - Maui o levarão a uma ilha diferente, mas que fica a apenas 15 quilômetros de distância e é facilmente acessível por via aérea. Esta ilha tem uma população muito pequena com uma cultura e paisagens diferentes. Todo o litoral do nordeste é margeado por falésias e praias remotas. Eles são completamente inacessíveis por qualquer outro meio de transporte além do ar.\n"
"As pessoas que vivem na ilha nunca viram essa paisagem notável, a menos que tenham feito passeios de helicóptero em Maui para vê-la. Quando o tempo está chuvoso ou quando há muitas chuvas na estação, você pode ver muitas cachoeiras surpreendentes."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"Mais importante para o viajante em família do que para o viajante a "
"negócios, você deve descobrir se o hotel é adequado para crianças no "
"diretório e tomar sua decisão a partir daí. Um aspecto que vale a pena "
"observar é se o hotel oferece serviço de babá. Para o viajante a negócios "
"que deseja escapar das crianças, isso também é muito relevante – talvez um "
"hotel que não seja adequado para crianças seja mais apropriado!"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_most_viewed_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "Publicações mais visualizadas"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "Nome"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "New Blog Post"
msgstr "Nova postagem do blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "Nenhum post de blog."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "Número de visualizações"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr "Nenhum resultado para \"%s\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "Nenhum resultado encontrado para '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "Nenhum marcador definido ainda."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "Nenhum"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"Nada disso o impede de seguir em frente com seus planos de montar um "
"incrível sistema de telescópio. Apenas procure conselho e treinamento de "
"qualidade sobre como configurar seu telescópio para atender às suas "
"necessidades. Usando essas sugestões, você desfrutará de horas de prazer "
"observando as vistas fenomenais do céu noturno que estão além do olho nu."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"Não apenas saber o clima garantirá que sua observação das estrelas seja "
"recompensadora, mas se você souber quando as grandes chuvas de meteoros e "
"outros grandes eventos astronômicos acontecerão, isso fará com que a emoção "
"da astronomia seja ainda mais intensa."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de Ações"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "Número de Erros"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensagens que requer uma ação"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensagens com erro de entrega"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread_counter
msgid "Number of unread messages"
msgstr "Quantidade de mensagens não lidas"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"É claro que, para levar sua adoração à lua ao máximo, subir de nível "
"adquirindo um bom telescópio de iniciante fornecerá os detalhes mais "
"impressionantes da superfície lunar. Com cada uma dessas atualizações, seu "
"conhecimento e a profundidade e abrangência do que você poderá ver "
"melhorarão geometricamente. Para muitos astrônomos amadores, não dá pra "
"enjoar de ver este nosso objeto espacial mais próximo."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Once you have reviewed the content on mobile, close the preview."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "Outros"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "Nossos blogs"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "Foto por  Anton Repponen, @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "Foto por  Arto Marttinen, @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "Foto por Boris Smokrovic, @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "Foto por  Denys Nevozhai, @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr "Foto por Greg Rakozy, @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "Foto por Jason Briscoe, @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "Foto por Jon Ly, @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "Foto por Patrick Brinksma, @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "Foto por PoloX Hernandez, @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "Foto por SpaceX, @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "Foto por Teddy Kelley, @teddykelley"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "Postagens"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Data de publicação"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "Publicado ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "Data Publicada"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "Postagem Publicada"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "Opções de Publicação"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "Data de Publicação"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Read more"
msgstr "Leia mais"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"fa fa-chevron-right ml-2\"/>"
msgstr "Ler mais <i class=\"fa fa-chevron-right ml-2\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "Restaurantes, Cafés e Bares"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict publishing to this website."
msgstr "Restringir publicação a este site."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "Mecanismos de Pesquisa Otimizados"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erro no envio de SMS"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "Exemplo"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Satellites"
msgstr "Satélites"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Search for an image. (eg: type \"business\")"
msgstr "Pesquisar por uma imagem. (ex: tipo \"negócios\")"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seaside vs mountain side"
msgstr "Perto do mar ou das montanhas"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seeing the world from above"
msgstr "Vendo o mundo lá de cima"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "Select Blog"
msgstr "Selecionar Blog"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select the blog you want to add the post to."
msgstr "Selecione o blog que você deseja para adicionar a postagem."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr "Selecione este item de menu para criar uma nova postagem no blog."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "Nome Seo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "Separe cada keyword com uma vírgula"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Set a blog post <b>cover</b>."
msgstr "Defina uma <b>capa</b> para o post do blog."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"Vários pássaros migratórios e nativos, mamíferos e répteis chamam o Copper "
"Canyon de lar. Também vale a pena conferir a fauna requintada nesta terra "
"quase intocada."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "Compartilhar no Facebook"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "Compartilhar no Linkedin"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Twitter"
msgstr "Compartilhar no Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "Compartilhe este post"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
msgid "Sierra Tarahumara"
msgstr "Sierra Tarahumara"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr ""
"Sierra Tarahumara, popularmente conhecida como Copper Canyon, está situada "
"no México. A área é um destino favorito entre aqueles que procuram umas "
"férias de aventura."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Silly-Chico"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Skies"
msgstr "Céus"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"Portanto, é extremamente importante que você obtenha o telescópio certo para"
" onde você está e quais são suas preferências de observação de estrelas. "
"Para começar, vamos discutir os três principais tipos de telescópios e, em "
"seguida, estabelecer alguns conceitos iniciais para aumentar suas chances de"
" comprar a coisa certa."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"Então pode ser naquelas férias uma vez ao ano em um acampamento ou uma "
"viagem para a casa de um parente no campo. Se nos encontrarmos do lado de "
"fora, então poderemos ver o esplendor do céu noturno fazer seu show "
"espetacular. Se você já teve aquele tipo de momento em que ficou "
"literalmente sem fôlego com o esplendor que o céu noturno pode nos mostrar, "
"provavelmente se lembra do momento exato em que não dá pra dizer muito além "
"de “uau” sobre o que viu."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"Portanto, para selecionar o tipo certo de telescópio, seus objetivos ao usar"
" o telescópio são importantes. Para realmente entender os pontos fortes e "
"fracos, não apenas das lentes e do design do telescópio, mas também de como "
"o telescópio funciona em várias situações de observação de estrelas, é "
"melhor fazer o dever de casa com antecedência e testar os diferentes tipos. "
"Por isso, antes de fazer sua primeira compra..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr ""
"Você vai viajar para o exterior, já decidiu o destino e agora precisa "
"escolher um hotel."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr "Alguém famoso no <cite title=\"Source Title\">título de origem</cite>"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Spotting the fauna"
msgstr "Explorando a fauna"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/models/website_blog.py:0
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Start writing here..."
msgstr "Comece a escrever aqui..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "Estilo"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "Legenda"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "Subtítulo"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "Categorias de etiquetas"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "Categoria de marcador"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr "Formulário de Categoria de Marcador"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "Formulário de Marcador"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "Lista de Marcadores"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists !"
msgstr "A categoria de marcador já existe!"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Esse marcador já existe !"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "Marcadores"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Taking pictures in the dark"
msgstr "Tirando fotos no escuro"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
msgid "Teaser"
msgstr "Prévia"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "Conteúdo da Prévia"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"Dez anos atrás, você provavelmente teria visitado seu agente de viagens "
"local e confiado nos conselhos pessoais que recebia dos chamados "
"'especialistas'. A maneira do século 21 de selecionar e reservar seu hotel "
"é, obviamente, na internet, usando sites de viagens."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"Esse momento “uau” é a essência da astronomia. Para alguns, esse momento "
"ocasiona uma paixão que os leva a uma carreira estudando as estrelas. Para "
"alguns sortudos, esse momento ocasiona uma obsessão que os leva a viajar "
"para as estrelas em um ônibus espacial ou em uma de nossas primeiras missões"
" espaciais. Mas para a maioria de nós, a astronomia pode se tornar um "
"passatempo ou um hobby. Mas carregamos esse momento conosco pelo resto de "
"nossas vidas e começamos a procurar maneiras de olhar mais profundamente e "
"aprender mais sobre o universo espetacular que vemos nos milhões de estrelas"
" acima de nós todas as noites."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr "A beleza da astronomia é que qualquer um pode estudá-la."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"A melhor hora para ver a lua, obviamente, é à noite, quando há poucas nuvens"
" e o clima é propício para um longo estudo. O primeiro trimestre produz o "
"maior detalhe de estudo. E não se engane com o “sumiço” de parte da lua "
"quando não está no estágio de lua cheia. O fenômeno conhecido como “brilho "
"da terra” também lhe dá a capacidade de ver a parte escura da lua com alguns"
" detalhes, mesmo que a lua esteja mostrando apenas um quarto ou metade dela."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "O melhor momento para ver a lua."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr ""
"A publicação ficará visível no site para os seus visitantes a partir desta "
"data se estiver definido como publicada."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"Os penhascos desta região estão entre os mais altos do mundo e ver a água "
"caindo dos altos picos é simplesmente de tirar o fôlego. O curto passeio de "
"Maui de helicóptero vale a pena para ver a beleza deste ambiente natural."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr "A URL completa para acessar o documento através do site."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"A próxima coisa que naturalmente queremos é um bom telescópio. Você pode ter"
" visto um aquarista que está bem adiantado em seu estudo instalando aqueles "
"telescópios de aparência muito legal em uma colina em algum lugar. Isso "
"excita o astrônomo amador que existe em você, porque esse deve ser o próximo"
" passo lógico no crescimento do seu hobby. Mas como comprar um bom "
"telescópio pode ser totalmente confuso e intimidante."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"O site deve oferecer uma análise detalhada dos serviços de lazer dentro do "
"hotel – spa, piscina, academia, sauna – bem como detalhes de quaisquer "
"outras instalações próximas, como campos de golfe. 7. Necessidades "
"especiais: o site do diretório de hotéis deve informar o visitante sobre os "
"serviços para necessidades especiais e a política de acessibilidade de cada "
"hotel. Embora, novamente, isso não se aplique a todos os visitantes, é "
"absolutamente vital para alguns."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr ""
"O tripé ou outros acessórios mudarão significativamente com um telescópio "
"que ficará em seu deck comparado a um que você planeja levar para locais "
"remotos."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"A visão disso é realmente de tirar o fôlego e é uma visão que não se pode "
"perder. Também é altamente educativo, tendo a chance de ver de perto um "
"vulcão adormecido, algo que não pode ser visto todos os dias. Nos lados "
"norte e sul do vulcão, você verá uma visão diferente incrível. Esses lados "
"são exuberantes e verdes e você poderá ver algumas belas cachoeiras e lindos"
" arbustos. As florestas tropicais são abundantes neste lado da ilha, e é "
"algo que não é facilmente acessível por qualquer outro meio que não seja o "
"ar."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"E então, há o problema da motivação do revisor. Quanto mais comentários você"
" lê, mais percebe como eles tendem a se agrupar nos extremos de opinião. De "
"um lado, você tem revisores furiosos levantando machados; no outro, você "
"encantou os convidados, que dispensam elogios inacreditavelmente. Não é "
"surpreendente saber que os hotéis às vezes publicam suas próprias críticas "
"elogiosas, ou procuram qualquer problema para ter a chance de criticar a "
"concorrência com críticas negativas. Faz sentido considerar o que é "
"realmente importante para você ao selecionar um hotel. Você deve então "
"escolher um diretório de hotéis online que forneça informações atualizadas, "
"independentes e imparciais, que realmente importam."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr "Há outras considerações a fazer antes de tomar uma decisão de compra."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""
"Há algo atemporal sobre o cosmos. O fato de que os planetas, a lua e as "
"estrelas além deles estão lá há eras mexe com o senso de nosso lugar no "
"universo. Na verdade, muitas das estrelas que “vemos” a olho nu são, na "
"verdade, luz que veio daquela estrela centenas de milhares de anos atrás. "
"Essa luz está agora mesmo alcançando a Terra. Então, de uma forma muito "
"real, olhar para cima é como viajar no tempo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""
"Essas coisas realmente importam e qualquer diretório de hotel decente deve "
"dar a você esse tipo de conselho sobre quartos - não só o número de quartos,"
" que é a opção comum!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr "Esta caixa não será visível a seus visitantes"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "This tag already exists"
msgstr "Este marcador já existe"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Tiny"
msgstr "Pequeno"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "Título"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""
"Para olhar a lua a olho nu, familiarizar-se com o mapa lunar ajudará você a "
"identificar os mares, crateras e outros fenômenos geográficos que outros já "
"mapearam para tornar seu estudo mais agradável. Os mapas lunares podem ser "
"adquiridos em qualquer loja de astronomia ou online e valem o investimento."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""
"Para começar a aprender a observar as estrelas muito melhor, existem algumas"
" coisas básicas que talvez precisemos observar mais profundamente, além do "
"que podemos ver a olho nu, e começar a estudar as estrelas além de apreciá-"
"las. A primeira coisa que você precisa não é equipamento, mas literatura. Um"
" bom mapa estelar mostrará as principais constelações, a localização das "
"principais estrelas que usamos para navegar no céu e os planetas que "
"parecerão maiores que as estrelas. E se você adicionar a esse mapa alguns "
"materiais introdutórios bem elaborados ao hobby da astronomia, estará no "
"caminho certo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""
"Para aumentar ainda mais o nível, um bom par de binóculos pode fazer "
"maravilhas pelos detalhes que você verá na superfície lunar. Para obter os "
"melhores resultados, obtenha um bom campo amplo nas definições dos binóculos"
" para que possa contemplar a paisagem lunar em toda a sua beleza. E porque é"
" quase impossível manter os binóculos parados por muito tempo que você "
"deseje contemplar este magnífico corpo no espaço, você pode querer adicionar"
" ao seu arsenal de equipamentos um bom tripé onde você pode fixar os "
"binóculos para que possa estude a lua com conforto e com uma plataforma de "
"observação estável."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""
"Para levá-lo a um próximo nível, você pode aproveitar parcerias com outros "
"astrônomos ou visitar um dos grandes telescópios que foram montados por "
"profissionais que investiram em melhores técnicas para eliminar a "
"interferência atmosférica para ver a lua melhor ainda. A internet pode lhe "
"dar acesso ao Hubble e a muitos dos enormes telescópios que estão apontados "
"para a lua o tempo todo. Além disso, muitos clubes de astronomia estão "
"trabalhando em maneiras de combinar vários telescópios, cuidadosamente "
"sincronizados com computadores para a melhor visão da paisagem lunar."

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr "Viagem"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "Não publicado ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread
msgid "Unread Messages"
msgstr "Mensagens não lidas"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contador de mensagens não lidas"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "Publicação sem Título"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr ""
"Use este ícone para pré-visualizar seu post em <b>dispositivos móveis</b>."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "Usado em:"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Viewpoints"
msgstr "Pontos de vista"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "Visualizações"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr "Visível nas páginas de todos os blogs'"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "Visível neste site"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "WRITE HERE OR DRAG BUILDING BLOCKS"
msgstr "ESCREVA AQUI OU ARRASTE BLOCOS DE CONSTRUÇÃO"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "Website"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "Blogs do site"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "Mensagens do site"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtro de Snippet do Site"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "URL do site"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "Histórico de comunicação do site"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "Meta-descrição do site"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Palavras-chave do Site"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "Meta título site"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imagem de gráfico aberto do site"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr "E se eles te deixassem usar o Hubble"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""
"Enquanto qualquer um pode olhar para cima e se apaixonar pelas estrelas a "
"qualquer momento, a diversão da astronomia é aprender como se tornar cada "
"vez mais habilidoso e equipado na observação de estrelas para ver e entender"
" cada vez mais a cada vez que olha para cima. Aqui estão alguns passos que "
"você pode tomar para tornar os momentos se dedicando ao seu hobby de "
"astronomia muito mais agradáveis."

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "With a View"
msgstr "Com uma visualização"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr "Escreva um pequeno texto aqui para descrever o blog de sua empresa."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Write a title, the subtitle is optional."
msgstr "Escreva um título, o subtítulo é opcional."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""
"Você deve sempre ponderar cuidadosamente os tipos de instalações que "
"necessita para o seu quarto e encontrar o hotel que tenha aquelas que "
"considere importantes. O site do diretório de hotéis deve detalhar questões "
"como: tamanho da cama, acesso à internet (seu custo, se há Wi-Fi ou conexão "
"de banda larga com fio), comodidades de cortesia, vistas do quarto e "
"serviços de luxo, como menu de travesseiros ou menu de banho, escolha de "
"quartos para fumantes ou não fumantes etc."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""
"Você verá toda a beleza que Maui tem a oferecer e poderá se divertir muito "
"com toda a família. Os passeios não são muito caros e duram de 45 minutos a "
"mais de uma hora. Você pode ver lugares que normalmente são inacessíveis com"
" os passeios de helicóptero em Maui. Lugares que não estão disponíveis a pé "
"ou de veículo podem ser vistos por via aérea. Vistas deslumbrantes aguardam "
"aqueles que estão dispostos a fazer passeios divertidos de helicóptero em "
"Maui. Se você vai ficar na ilha por um período de tempo razoável, considere "
"fazer vários passeios de helicóptero em Maui."

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr "aventura"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr "blog. Clique aqui para acessar o blog :"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr "Detalhes"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "por"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr "descoberta"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr "guias"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "foi publicado em"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr "hotéis"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "em"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "in <i class=\"fa fa-folder-open text-muted\"/>"
msgstr "em <i class=\"fa fa-folder-open text-muted\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "in <i class=\"fa fa-folder-open text-white-75\"/>"
msgstr "em <i class=\"fa fa-folder-open text-white-75\"/>"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr "telescópios"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr "deixar um comentário"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "não publicado"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted mr-1\"/>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr "| Nenhum comentário ainda"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "⌙ Hover effect"
msgstr ""
