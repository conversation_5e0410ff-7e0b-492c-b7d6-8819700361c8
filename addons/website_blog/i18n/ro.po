# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-14 15:42+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "#{year}"
msgstr ""

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copie)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr "' antet pagină."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr "'. Afișare rezultate pentru '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr "-- Toate datele"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""
"<b>Binoclul este ușor și portabil.</b> Dacă nu aveți luxul să înființați și "
"să operați un observator de pe puntea dvs., probabil că veți călători pentru"
" a vă viziona. Binoclurile merg cu tine mult mai ușor și sunt mai ușoare de "
"transportat în țară și de folosit în timp ce ești acolo decât un set greoi "
"de instalare a telescopului."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Click on Save</b> to record your changes."
msgstr "<b>Clicl pe salvare</b> pentru a vă înregistra schimbările."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""
"<b>Alegeți creierul experților</b>.Dacă nu sunteți deja activ într-o "
"societate sau club de astronomie, oamenii de vânzări de la magazinul de "
"telescoape vă vor putea îndruma către societățile active din zona dvs. Odată"
" ce aveți conexiuni cu oameni care au cumpărat telescoape, puteți primi "
"sfaturi despre ceea ce funcționează și ce să evitați este mai valabil decât "
"orice veți obține de la un articol web sau de la un agent de vânzări de la "
"Wal-Mart."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr ""
"<b>Publicați-vă postarea de blog</b> pentru a fi vizibilă vizitatorilor"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>Logare</b>"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>That's it, your blog post is published!</b> Discover more features "
"through the <i>Customize</i> menu."
msgstr ""
"<b>Asta e, postarea dumneavoastră a fost publicată!</b> Descoperiți mai "
"multe caracteristici prin meniul de <i>Personalizare</i>."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""
"<b>TÎncercați înainte de a cumpăra.</b> Acesta este un alt avantaj al "
"excursiilor cu clubul de astronomie. Puteți lăsa deoparte câteva ore de "
"calitate cu oameni care cunosc telescoape și au instalate platformele lor "
"pentru a-și examina echipamentele, pentru a afla aspectele tehnice cheie și "
"pentru a le încerca înainte de a scufunda banii în propriul set."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>Scrie-ți povestea aici.</b> Utilizați bara de instrumente de sus pentru a"
" vă stiliza textul: adăugați o imagine sau un tabel, setați caractere aldine"
" sau cursive etc. Trageți și fixați blocuri de construcție pentru bloguri "
"mai grafice."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr ""
"<em class=\"h4 my-0\">În afară de populația nativă, viața sălbatică locală "
"este, de asemenea, o atracție majoră a mulțimii.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr ""
"<em class=\"h4 my-0\">Este extrem de important să luați doar telescopul "
"potrivit.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr "<em class=\"h4 my-0\">Momentul “Wow” este tot ce este astrologia.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">Există ceva atemporal în cosmos.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr ""
"<em class=\"h4 my-0\">Studiul dvs. asupra lunii, ca orice altceva, poate "
"merge de la simplu la foarte complex.</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>Nu sunt etichete definite</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Post date\" title=\"Post date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-sm\">Read "
"Next</span>"
msgstr ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-sm\">Citiți "
"următorul</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"<i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"mr-1\">Show:</span>"
msgstr "<span class=\"mr-1\">Arată:</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled pl-0\">Blogs:</span>"
msgstr "<span class=\"nav-link disabled pl-0\">Bloguri:</span>"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "O modalitate excelentă de a descoperi locuri ascunse"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr ""
"O vacanță la Copper Canyon promite să fie un amestec captivant de relaxare, "
"cultură, istorie, viață sălbatică și drumeții."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "O nouă postare"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"Un călător poate alege să exploreze zona făcând drumeții în jurul canionului"
" sau aventurându-se în el. Este necesară o planificare detaliată pentru cei "
"care doresc să se aventureze în adâncurile canionului. Există o serie de "
"companii de turism specializate în organizarea de tururi în regiune. "
"Vizitatorii pot zbura la Copper Canyon folosind o viză de turist, valabilă "
"180 de zile. Călătorii pot conduce, de asemenea, de oriunde din Statele "
"Unite și pot obține o viză la stația vamală mexicană de la graniță."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "Despre noi"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
"Mai presus de toate,<b> stabiliți o relație cu un magazin de telescoape de "
"renume</b> care angajează oameni care cunosc lucrurile. Dacă vă cumpărați "
"telescopul la un magazin Wal-Mart sau la un magazin universal, șansele de a "
"obține ceea ce trebuie sunt mici."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "Accesează postarea"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "Intervenție necesară"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "Activ"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "Adăugați ceva"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "Tot"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "Toate Blogurile"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "Toate blogurile"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Alone in the ocean"
msgstr "Singur în ocean"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "În această direcție, cât de dificilă este instalarea și defectarea?"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.js:0
#, python-format
msgid "Amazing blog article: %s! Check it live: %s"
msgstr "Articol Blog Uimitor%s! Verifică-l în direct: %s"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr ""
"Un amestec captivant de relaxare, cultură, istorie, animale sălbatice și "
"drumeții."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"And when all is said and done,<b> echipați-vă</b>. Căutarea dvs. de "
"telescoape mai noi și mai bune va fi una pe tot parcursul vieții. Lasă-te "
"dependent de astronomie și experiența va îmbogăți fiecare aspect al vieții. "
"Va fi o dependență pe care nu vrei să o rupi niciodată."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"O altă caracteristică unică a Copper Canyon este prezența culturii indiene "
"Tarahumara. Acești oameni semi-nomazi trăiesc în locuințe rupestre. Mediul "
"lor de trai depinde în principal de agricultură și creșterea vitelor."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
msgid "Archive"
msgstr "Arhivează"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "Arhivat"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
msgid "Archives"
msgstr "Arhive"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "Articol"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "Articole"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "Astronomie"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"Cluburile de astronomie sunt locuri pline de viață, pline de amatori "
"cunoscuți, cărora le place să vă împărtășească cunoștințele. Pentru prețul "
"unei cocsuri și gustări, ei vor merge stele privindu-vă cu voi și vă vor "
"copleși cu trivia și cunoaștere mare"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "Astronomia “privește stelele”"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Atom Feed"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Autor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "Nume autor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "Avatar"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Awesome hotel rooms"
msgstr "Camere de hotel minunate"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr "Fiți conștient de acest lucru numit “astronomie”"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"Devenind parte a societății astronomilor amatori devotați, veți avea acces "
"la aceste eforturi organizate pentru a atinge noi niveluri în capacitatea "
"noastră de a studia luna Pământului. Și vă va oferi colegi și prieteni care "
"vă împărtășesc pasiunea pentru astronomie și care își pot împărtăși "
"experiența și domeniile de expertiză în timp ce căutați să aflați unde ați "
"putea privi în imensul cer de noapte, la lună și dincolo de ea în căutarea "
"cunoașterea despre universul aparent nesfârșit de deasupra noastră."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "Devenind parte a societății dedicate astronomilor amatori."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "Dotări pentru dormitor"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"Înainte de a merge la acea cheltuială mare, ar putea fi un pas mai bun din "
"ochiul liber să investești într-un set bun de binocluri. Există chiar și "
"binocluri care sunt potrivite pentru privirea stelelor, care vor face la fel"
" de bine o treabă oferindu-vă acea viziune suplimentară pe care doriți să o "
"vedeți puțin mai bine minunile universului. Un set de binocluri bine "
"conceput vă oferă, de asemenea, mult mai multă mobilitate și capacitate de a"
" vă menține “viziunea îmbunătățită” la îndemână, atunci când acea vedere "
"uimitoare tocmai vi se prezintă."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "Înainte de a face prima achiziție ..."

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "Dincolo de ochi"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog"
msgstr "Blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "Nume Blog"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog Post"
msgstr "Postare Blog"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to be calling this file !"
msgstr "Postarea pe blog <b>%s</b> pare să apeleze acest fișier!"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to have a link to this page !"
msgstr "Postare Blog<b>%s</b> pare să aibă un link către această pagină!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "Coperta postării pe blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "Titlu Postare Blog"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.actions.act_window,name:website_blog.action_blog_post
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
#, python-format
msgid "Blog Posts"
msgstr "Postări pe blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "Subtitlu Blog"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "Etichetă blog"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "Categorie etichetă blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "Etichete Blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "Titlu de Blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "Titlul Blogului"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr "Jurnale"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"Dar cum treceți în revistă opțiunile uimitoare oferite? Și, mai important, "
"chiar aveți încredere în fotografiile și descrierile hotelurilor pe care și "
"le-au acordat cu motivația de a primi rezervări? Recenziile călătorilor pot "
"fi utile, dar trebuie să aveți grijă. Ele sunt adesea părtinitoare, uneori "
"depășite, și este posibil să nu vă servească deloc interesele. De unde știți"
" că caracteristicile importante pentru examinator sunt importante pentru "
"tine?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "Cumpărarea unui telescop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr ""
"Cumpărarea telescopului potrivit pentru a vă duce dragostea de astronomie la"
" nivelul următor este un mare pas următor în dezvoltarea pasiunii pentru "
"stele."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "Poate Publica"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "Categorie"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "Facilități pentru copii"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Choose an image from the library."
msgstr "Alegeți o imagine din bibliotecă."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Click here to add new content to your website."
msgstr "Faceți clic aici pentru a adăuga conținut nou pe site-ul dvs. web."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr ""
"Clic pe \"<b>Nou</b>\" în colțul de dreapta sus pentru a scrie prima postare"
" blog."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "Închide"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "Comentariu"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Comments"
msgstr "Comentarii"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "Conținut"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"Copper Canyon este unul dintre cele șase chei din zonă. Deși numele "
"sugerează că defileul ar putea avea o anumită relevanță pentru exploatarea "
"cuprului, acest lucru nu este cazul. Numele este derivat din lichenul de "
"cupru și verde care acoperă canionul. Copper Canyon are două zone climatice."
" Regiunea prezintă un climat alpin în partea de sus și un climat subtropical"
" la nivelurile inferioare. Iernile sunt reci, cu furtuni de zăpadă frecvente"
" la altitudini mai mari. Verile sunt uscate și fierbinți. Capitala, "
"Chihuahua, este un deșert la mare altitudine, unde vremea variază de la "
"ierni reci până la veri calde. Regiunea este unică datorită diverselor "
"ecosisteme care există în interiorul ei."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "Proprietăți Copertă"

#. module: website_blog
#: model_terms:ir.actions.act_window,help:website_blog.action_blog_post
msgid "Create a new blog post"
msgstr "Creați o nouă postare blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "Creat în"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Data (nou la vechi)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Data (vechi la nou)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "Descriere"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "Dexter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_edit_options
msgid "Duplicate"
msgstr "Duplicare"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "Estul Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"Tururile cu elicopterul din Estul Maui vă vor oferi o vedere asupra "
"vulcanului de zece mii de picioare, Haleakala sau Casa soarelui. Acest "
"vulcan este latent și a erupt ultima dată în 1790. Veți putea vedea craterul"
" vulcanului și pământul uscat și arid care înconjoară partea de sud a pantei"
" vulcanului cu tururi cu elicopterul Maui."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "Editează în spate"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "Editați '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "Editați antetul paginii „Toate blogurile”."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "Editați antetul paginii „Filtrare rezultate”."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "Facebook"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "Fapte pe care ar trebui să le rețineți."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"În cele din urmă și cel mai important, echipa de inspecție a directorului "
"hotelier de calitate ar fi trebuit să viziteze hotelul în cauză în mod "
"regulat, să se întâlnească cu personalul, să se culce într-un dormitor și să"
" încerce mâncarea. Ei ar trebui să experimenteze hotelul așa cum o poate "
"face doar oaspeții hotelului și abia atunci sunt într-o poziție puternică "
"pentru a scrie despre hotel."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Follow Us"
msgstr "Urmărește-ne"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "Persoane interesate"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"Pentru mulți dintre noi, care locuim în oraș, nu prea observăm cerul acela "
"acolo sus, în mod obișnuit. Luminile orașului fac o treabă bună de a masca "
"afișajul uimitor care este deasupra tuturor capetelor noastre tot timpul."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr ""
"Pentru mulți dintre noi, prima noastră experiență de a învăța despre "
"corpurile cerești începe atunci când am văzut prima noastră lună plină pe "
"cer. Este cu adevărat o priveliște magnifică chiar și cu ochiul liber."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"De la cel mai mic bebeluș până la cel mai avansat astrofizician, există ceva"
" pentru oricine dorește să se bucure de astronomie. De fapt, este o știință "
"atât de accesibilă încât practic oricine o poate face practic oriunde s-ar "
"afla. Tot ce trebuie să știe să facă este să privească în sus."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "Ia-ți un tocilar"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "Ia un telescop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "Obțineți o istorie"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "Să începem"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Grupează după"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr ""
"Iată câteva dintre faptele cheie pe care ar trebui să le aveți în vedere:"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "Sfaturi de vacanță"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "Cum să căutați"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"Cât de complex este telescopul și veți avea probleme cu întreținerea? Rețea "
"pentru a obține răspunsurile la aceste și la alte întrebări. Dacă îți faci "
"astfel de teme, vei găsi doar telescopul potrivit pentru următorul mare pas "
"în evoluția pasiunii tale pentru astronomie."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "Cât de mobil trebuie să fie telescopul tău?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
msgid "How to choose the right hotel"
msgstr "Cum să alegeți hotelul potrivit"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread
msgid "If checked, new messages require your attention."
msgstr "Dacă este selectat, mesajele noi necesită atenția dumneavoastră."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, unele mesaje au o eroare de livrare."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"Dacă este important ca hotelul dvs. să fie, de exemplu, pe plajă, aproape de"
" parcul tematic sau convenabil pentru aeroport, atunci locația este "
"primordială. Orice director decent ar trebui să ofere o hartă a locației "
"hotelului și a împrejurimilor sale. Ar trebui să existe diagrame la distanță"
" până la aeroport oferite, precum și o formă de hartă interactivă."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"Dacă noaptea este senină, puteți vedea detalii uimitoare ale suprafeței lunare, doar stea care se uită în curtea din spate.\n"
"Bineînțeles, pe măsură ce crești în dragostea ta pentru astronomie, vei găsi multe corpuri cerești fascinante. Dar luna poate fi întotdeauna prima noastră iubire, deoarece este obiectul spațial îndepărtat care are distincția unică de a zbura aproape de pământ și pe care a mers omul."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
msgid "In"
msgstr "În"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"În multe privințe, este un pas mare de la cineva care doar se prostește cu "
"astronomia la un student serios al științei. Dar amândoi știm că există încă"
" un alt pas important după ce ați cumpărat un telescop înainte de a ști cu "
"adevărat cum să-l folosiți."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
msgid "Is Published"
msgstr "Este Publicat"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Islands"
msgstr "Insule"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"Este foarte distractiv să începi să înveți constelațiile, cum să navighezi "
"pe cerul nopții și să găsești planetele și celebrele stele. Există site-uri "
"web și cărți la îndemână pentru a vă ghida."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"Este important să alegeți un hotel care să vă facă să vă simțiți confortabil"
" – mobilier contemporan sau tradițional, decor local sau internațional, "
"formal sau relaxat. Directorul hotelier ideal ar trebui să vă informeze "
"despre opțiunile disponibile."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr ""
"Este sigur să spunem că la un moment dat al vieții noastre, fiecare dintre "
"noi are acel moment în care suntem brusc uimiți când ne întâlnim față în "
"față cu enormitatea universului pe care îl vedem pe cerul nopții."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"Este cu adevărat uimitor când te gândești la asta că doar prin privirea în "
"sus într-o anumită noapte, ai putea vedea practic sute de mii de stele, "
"sisteme stelare, planete, luni, asteroizi, comete și poate chiar și o navetă"
" spațială ocazională ar putea rătăci prin . Este și mai uluitor când îți dai"
" seama că cerul la care te uiți este din toate punctele de vedere același "
"cer de care strămoșii noștri s-au bucurat acum sute și mii de ani când "
"tocmai au ridicat privirea."

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Jungle"
msgstr "Junglă"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "Știți la ce vă uitați"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "Știți când să vă uitați"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Large"
msgstr "Larg"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "Ultimul colaborator"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Latest"
msgstr "Ultimul"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_latest_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "Ultimele postări de pe blog"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"Învățarea fundalului marilor descoperiri în astronomie va face ca momentele "
"tale să privească în stele mai semnificative. Este una dintre cele mai vechi"
" științe de pe pământ, așa că aflați marii istoriei care au privit aceste "
"stele înaintea voastră."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "Facilitati de relaxare"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"Culoarea locală este excelentă, dar restaurantele și barurile hotelului pot "
"juca un rol important în șederea dumneavoastră. Ar trebui să fiți conștienți"
" de alegere, stil și dacă sunt sau nu inteligenți sau informali. Un raport "
"bun despre hotel ar trebui să vă spună acest lucru și, în special, despre "
"facilitățile de mic dejun."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "Locație"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_main_attachment_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_main_attachment_id
msgid "Main Attachment"
msgstr "Atașament principal"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "Marley"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Maui helicopter tours"
msgstr "Tururi cu elicopterul Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"Tururile cu elicopterul Maui sunt o modalitate excelentă de a vedea insula "
"dintr-o perspectivă diferită și de a avea o aventură distractivă. Dacă nu "
"ați fost niciodată cu un elicopter înainte, acesta este un loc minunat "
"pentru a face acest lucru."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"Tururile cu elicopterul Maui sunt o modalitate excelentă de a vizita acele "
"locuri în care nu se poate ajunge pe jos sau cu mașina. Tururile durează "
"aproximativ o oră și variază de la aproximativ o sută opt cinci dolari la "
"două sute patruzeci de dolari persoană. Pentru mulți, aceasta este o ocazie "
"o dată în viață de a vedea peisaje naturale care nu vor mai fi disponibile "
"din nou. Luarea de camere și videoclipuri pentru a surprinde momentele vă va"
" permite, de asemenea, să retrăiți turul din nou și din nou, pe măsură ce vă"
" amintiți de-a lungul anilor."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"Tururile cu elicopterul Maui vă vor permite să vedeți toate aceste "
"obiective. Asigurați-vă că luați o cameră sau un videoclip cu dvs. atunci "
"când faceți excursii cu elicopterul Maui pentru a surprinde frumusețea "
"peisajului și pentru a arăta prietenilor și familiei acasă toate lucrurile "
"minunate pe care le-ați văzut în vacanță."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Medium"
msgstr "Mediu"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "Eroare livrare mesaj"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "Meta Descriere"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "Cuvinte cheie meta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "Titlu Meta"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "Molokai Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"Tururile cu elicopterul Molokai Maui vă vor duce pe o insulă diferită, dar una care este la doar nouă mile distanță și ușor accesibilă pe calea aerului. Această insulă are o populație foarte mică, cu o cultură și un peisaj diferit. Întreaga coastă a nord-estului este căptușită cu stânci și plaje îndepărtate. Sunt complet inaccesibile prin orice alt mijloc de transport decât aerian.\n"
"Oamenii care locuiesc pe insulă nu au văzut niciodată acest peisaj remarcabil decât dacă au făcut tururi cu elicopterul Maui pentru a-l vedea. Când vremea a fost ploioasă și sunt multe precipitații pentru sezon, veți vedea multe cascade uimitoare."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"Mai important pentru călătorul de familie decât pentru călătorul de afaceri,"
" ar trebui să aflați cât de prietenos este hotelul din director și să luați "
"decizia de acolo. Un lucru care merită căutat este dacă hotelul oferă un "
"serviciu de baby sitting. Pentru călătorii de afaceri care doresc să scape "
"de copii, acest lucru este, desigur, foarte relevant –  poate că un hotel "
"care nu este potrivit pentru copii ar fi ceva mai potrivit!"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_most_viewed_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "Cele mai vizualizate postări de pe blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "Nume"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "New Blog Post"
msgstr "Postare nouă pe blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "Nici o postare pe blog încă."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "Număr de vizualizări"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr "Nu există rezultate pentru \"%s”."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "Nu sunt rezultate pentru '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "Nu există etichete definite încă."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "Fără"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"Nimic din toate acestea nu vă împiedică să mergeți mai departe cu planurile "
"voastre de a pune împreună un sistem minunat de telescop. Asigurați-vă că "
"primiți sfaturi de calitate și instruire cu privire la modul de configurare "
"a telescopului pentru a vă satisface nevoile. Folosind aceste linii "
"directoare, vă veți bucura de ore de plăcere la privirea stelelor la "
"obiectivele fenomenale din cerul nopții care sunt dincolo de ochiul liber."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"Nu numai că știți vremea, vă veți asigura că privirea stelară este plină de "
"satisfacții, dar dacă aflați când vor avea loc averse mari de meteori și "
"alte evenimente astronomice mari, veți face ca entuziasmul astronomiei să "
"prindă viață pentru voi."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "Numărul de erori"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Număr de mesaje ce necesită intervenție"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread_counter
msgid "Number of unread messages"
msgstr "Număr de mesaje necitite"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"Bineînțeles, pentru a vă duce închinarea la Lună la final, ridicarea "
"echipamentului până la un telescop de pornire bun vă va oferi cele mai "
"uimitoare detalii ale suprafeței lunare. Cu fiecare dintre aceste "
"actualizări, cunoștințele dvs., precum și profunzimea și scopul a ceea ce "
"veți putea vedea se vor îmbunătăți geometric. Pentru mulți astronomi "
"amatori, uneori nu putem obține suficient din ceea ce putem vedea pe cel mai"
" apropiat obiect spațial al nostru."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Once you have reviewed the content on mobile, close the preview."
msgstr ""
"După ce ați examinat conținutul de pe mobil, închideți previzualizarea."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "Altele"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "Blogurile noastre"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "Fotografie de Anton Repponen, @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "Fotografie de Arto Marttinen, @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "Fotografie de Boris Smokrovic, @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "Fotografie de Denys Nevozhai, @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr "Fotografie de Greg Rakozy, @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "Fotografie de Jason Briscoe, @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "Fotografie de Jon Ly, @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "Fotografie de Patrick Brinksma, @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "Fotografie de PoloX Hernandez, @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "Fotografie de SpaceX, @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "Fotografie de Teddy Kelley, @teddykelley"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "Postări"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Data publicării"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "Publicat ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "Dată publicată"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "Postare Publicată"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "Opțiuni publicare"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "Dată de publicare"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Read more"
msgstr "Citeste mai mult"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"fa fa-chevron-right ml-2\"/>"
msgstr "Citiți mai mult <i class=\"fa fa-chevron-right ml-2\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "Resturante, Cafenele și Baruri"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict publishing to this website."
msgstr "Limitați publicarea pe acest site web."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "Optimizat SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "Probă"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Satellites"
msgstr "Sateliți"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Search for an image. (eg: type \"business\")"
msgstr "Căutați o imagine. (de exemplu: tastați \"afacere\")"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seaside vs mountain side"
msgstr "Mare vs. munte"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seeing the world from above"
msgstr "Văzând lumea de sus"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "Select Blog"
msgstr "Selectați Blog"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select the blog you want to add the post to."
msgstr "Selectați blogul la care doriți să adăugați postarea."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr ""
"Selectați acest element de meniu pentru a crea o nouă postare pe blog."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "Nume SEO"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "Separați fiecare cuvânt cheie cu virgulă"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Set a blog post <b>cover</b>."
msgstr "Setați o postare de blog <b>copertă</b>."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"Mai multe păsări migratoare și native, mamifere și reptile numesc Copper "
"Canyon casa lor. Fauna rafinată din acest ținut aproape curat merită, de "
"asemenea, verificată."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "Distribuiți pe Facebook"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "Partajați pe Linkedln"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Twitter"
msgstr "Partajează pe Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "Partajați această postare"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
msgid "Sierra Tarahumara"
msgstr "Sierra Tarahumara"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr ""
"Sierra Tarahumara, cunoscută popular sub numele de Copper Canyon, este "
"situată în Mexic. Zona este o destinație preferată printre cei care caută o "
"vacanță plină de aventuri."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Silly-Chico"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Skies"
msgstr "Cer"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"Deci, este extrem de important să obțineți doar telescopul potrivit pentru "
"locul în care vă aflați și care sunt preferințele dvs. privind privirea la "
"stea. Pentru început, să discutăm cele trei tipuri majore de telescoape și "
"apoi să stabilim câteva concepte “Telescop 101” pentru a vă crește șansele "
"de a cumpăra ceea ce trebuie."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"Așadar, s-ar putea ca, o dată pe an, o vacanță la un loc de camping sau o "
"excursie la casa unei rude în țară, pe care o găsim afară, când cheltuitorul"
" cerului de noapte se hotărăște brusc să-și facă spectacolul spectaculos. "
"Dacă ați avut acel moment în care ați fost literalmente lovit fără suflare "
"de cheltuitorul pe care ni-l poate arăta cerul nopții, probabil că vă puteți"
" aminti acel moment exact în care ați putea spune puțin altceva decât “wow” "
"la ceea ce ați văzut."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"Deci, pentru a selecta tipul potrivit de telescop, obiectivele dvs. în "
"utilizarea telescopului sunt importante. Pentru a înțelege cu adevărat "
"punctele tari și punctele slabe ale lentilelor și ale designului "
"telescopului, ci și în ceea ce privește performanța telescopului în diferite"
" situații de observare a stelelor, este mai bine să faceți niște teme în "
"față și să vă expuneți la diferite tipuri. Deci, înainte de a face prima "
"achiziție ..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr ""
"Așadar, plecați în străinătate, v-ați ales destinația și acum trebuie să "
"alegeți un hotel."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr "Cineva Faimos în <cite title=\"Source Title\">Source Title</cite>"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Spotting the fauna"
msgstr "Observarea faunei"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/models/website_blog.py:0
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Start writing here..."
msgstr "Începeți scriind aici..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "Stil"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "Subtitlu"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "Subtitlu"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "Categorii Etichete"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "Categorie de etichete"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr "Formular de categorie de etichete"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "Formular de etichetare"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "Listă Etichete"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists !"
msgstr "Categoria de etichete există deja!"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Numele ales exista deja"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "Etichete"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Taking pictures in the dark"
msgstr "Realizare de fotografii în întuneric"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
msgid "Teaser"
msgstr "Teaser"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "Teaser Content"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"În urmă cu zece ani, probabil că ați fi vizitat agentul de turism local și "
"ați fi avut încredere în sfaturile față în față pe care vi le-au dat așa-"
"numiții ‘experții’. Modul secolului 21 de a selecta și rezerva hotelul dvs. "
"este, desigur, pe internet, utilizând site-uri web de călătorie."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"Momentul “Wow” este astrologia. Pentru unii, acel moment wow devine o "
"pasiune care duce la o carieră care studiază stelele. Pentru câțiva "
"norocoși, acel moment uimitor, deoarece o obsesie care consumă totul, care "
"îi determină să călătorească către stele în naveta spațială sau într-una din"
" misiunile noastre spațiale timpurii. Dar pentru majoritatea dintre noi "
"astrologia poate deveni o distracție sau un hobby obișnuit. Dar purtăm acel "
"moment wow cu noi pentru restul vieții noastre și începem să căutăm "
"modalități de a privi mai adânc și de a afla mai multe despre universul "
"spectaculos pe care îl vedem în milioanele de stele de deasupra noastră în "
"fiecare noapte."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr "Frumusețea astronomiei este că oricine o poate face."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"Cel mai bun moment pentru a vedea luna, evident, este noaptea când sunt "
"puțini nori și vremea este potrivită pentru un studiu lung și de durată. "
"Primul trimestru oferă cele mai mari detalii de studiu. Și nu vă lăsați "
"păcăliți, ci ștergerea unei părți a lunii atunci când nu se află în stadiul "
"de lună plină. Fenomenul cunoscut sub numele de “strălucire a pământului” vă"
" oferă posibilitatea de a vedea partea întunecată a lunii și cu unele "
"detalii, chiar dacă luna este afișată doar la sfert sau jumătate."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "Cel mai bun moment pentru a vedea luna."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr ""
"Postarea de pe blog va fi vizibilă pentru vizitatorii dvs. începând cu "
"această dată pe site, dacă este setată ca publicată."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"Stâncile din această regiune sunt printre cele mai înalte din lume și a "
"vedea apa în cascadă din vârfurile înalte este pur și simplu uluitor. Scurta"
" excursie de la Maui cu tururi cu elicopterul Maui merită să văd frumusețea "
"acestui mediu natural."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"URL-ul complet pentru accesarea documentului prin intermediul site-ului web."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"Următorul lucru pe care vrem să-l obținem în mod natural este un telescop "
"bun. S-ar putea să fi văzut un hobbyist care să lucreze bine în studiul lor "
"înființând undeva telescoapele cu aspect foarte cool pe un deal. Asta îl "
"entuziasmează pe astronomul amator din tine, deoarece acesta trebuie să fie "
"următorul pas logic în creșterea hobby-ului tău. Dar cum să cumperi un "
"telescop bun poate fi de-a dreptul confuz și intimidant."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"Site-ul ar trebui să ofere o analiză detaliată a serviciilor de agrement din"
" cadrul hotelului - spa, piscină, sală de gimnastică, saună - precum și "
"detalii despre orice alte facilități din apropiere, cum ar fi terenuri de "
"golf. 7. Nevoi speciale: site-ul directorului hotelier ar trebui să "
"informeze vizitatorul cu privire la serviciile și nevoile speciale de acces "
"ale fiecărui hotel. Deși acest lucru nu se aplică din nou fiecărui "
"vizitator, este absolut vital pentru unii."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr ""
"Trepiedul sau alte decizii accesorii se vor schimba semnificativ cu un "
"telescop care va locui pe punte față de unul pe care intenționați să îl "
"duceți în multe locații îndepărtate."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"Priveliștea este cu adevărat uluitoare și este o priveliște de neratat. "
"Este, de asemenea, extrem de educativ, cu șansa de a vedea de aproape un "
"vulcan adormit, ceva ce nu poate fi văzut în fiecare zi. Pe laturile nordice"
" și sudice ale vulcanului, veți vedea totuși o vedere incredibil de "
"diferită. Aceste părți sunt luxuriante și verzi și veți putea vedea câteva "
"cascade frumoase și o perie superbă. Pădurile tropicale tropicale abundă pe "
"această parte a insulei și este ceva care nu este ușor accesibil prin alte "
"mijloace decât prin aer."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"Apoi, există problema motivației recenzorului. Cu cât citești mai multe "
"recenzii, cu atât observi mai mult cum tind să se grupeze la extremele "
"opiniei. La un capăt, aveți recenzii supărați cu topoare; la cealaltă, ați "
"încântat oaspeții care aduc laudă dincolo de credință. Nu veți fi surprinși "
"să aflați că hotelurile își postează uneori propriile recenzii strălucitoare"
" sau că linia concurentului pentru șansa de a pierde concurența cu recenzii "
"proaste. Este logic să luați în considerare ceea ce este cu adevărat "
"important pentru dvs. atunci când alegeți un hotel. Apoi ar trebui să "
"alegeți un director hotelier online care să ofere informații actualizate, "
"independente, imparțiale, care contează cu adevărat"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr ""
"Există și alte considerații care trebuie luate în considerare în decizia "
"finală de cumpărare."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""
"Există ceva atemporal în cosmos. Faptul că planetele și luna și stelele de "
"dincolo de ele au fost acolo de veacuri ne ajută să simțim locul nostru în "
"univers. De fapt, multe dintre stelele pe care le “vedem” cu ochiul liber "
"sunt de fapt lumina care a venit de la steaua aceea cu sute de mii de ani în"
" urmă. Această lumină abia acum ajunge pe pământ. Deci, într-un mod foarte "
"real, privirea în sus este ca o călătorie în timp"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""
"Aceste lucruri chiar contează și orice director hotelier decent ar trebui să"
" vă ofere acest tip de sfaturi cu privire la dormitoare – nu doar numărul de"
" camere, care este opțiunea obișnuită!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr "Această casetă nu va fi vizibilă vizitatorilor dvs."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "This tag already exists"
msgstr "Această etichetă există deja"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Tiny"
msgstr "Micuț"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "Titlu"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""
"A privi luna cu ochiul liber, familiarizându-vă cu harta lunară vă va ajuta "
"să alegeți mările, craterele și alte fenomene geografice pe care alții le-au"
" cartografiat deja pentru a vă face studiul mai plăcut. Hărțile lunii pot fi"
" obținute de la orice magazin de astronomie sau online și merită investiția."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""
"Pentru a începe să învățăm cum să observăm stelele mult mai bine, există "
"câteva lucruri de bază de care ar trebui să ne uităm mai adânc, dincolo de "
"ceea ce putem vedea cu ochiul liber și să începem să studiem stelele și să "
"ne bucurăm de ele. Primul lucru de care aveți nevoie nu este deloc "
"echipament, ci literatura. O hartă bună a stelelor vă va arăta constelațiile"
" majore, locația stelelor cheie pe care le folosim pentru a naviga pe cer și"
" planetele care vor apărea mai mari decât stelele. Și dacă adăugați la acea "
"hartă câteva materiale introductive bine făcute în hobby-ul astronomiei, "
"sunteți pe drumul cel bun."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""
"Pentru a lovi o crestătură, o pereche bună de binocluri poate face minuni "
"pentru detaliile pe care le veți vedea pe suprafața lunară. Pentru cele mai "
"bune rezultate, obțineți un câmp larg bun în setările binoculare, astfel "
"încât să puteți admira peisajul lunar în toată frumusețea sa. Și pentru că "
"este aproape imposibil să țineți binoclul nemișcat pentru perioada de timp "
"în care veți dori să priviți acest corp magnific din spațiu, poate doriți să"
" adăugați la arsenalul echipamentului dvs. un trepied bun la care să puteți "
"fixa binoclul, astfel încât să puteți studiați luna confortabil și cu o "
"platformă stabilă de vizionare."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""
"Pentru a-l duce la un următor nivel natural, poate doriți să profitați de "
"parteneriatele cu alți astronomi sau vizitând unul dintre telescoapele cu "
"adevărat grozave care au fost înființate de profesioniști care au investit "
"în tehnici mai bune pentru a elimina interferențele atmosferice pentru a "
"vedea luna. chiar mai bine. Internetul vă poate oferi acces la Hubble și la "
"multe dintre telescoapele uriașe care sunt îndreptate către lună tot timpul."
" Mai mult, multe cluburi de astronomie lucrează la modalități de a combina "
"mai multe telescoape, sincronizate cu grijă cu computerele pentru a oferi "
"cea mai bună vedere a peisajului lunar."

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr "Călătorie"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "Nepublicat ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread
msgid "Unread Messages"
msgstr "Mesaje necitite"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contor mesaje necitite"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "Postare fără titlu"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr ""
"Folosiți această iconiță pentru a previzualiza postarea blog pe <b> "
"dispozitivele mobile</b>."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "Utilizat în:"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Viewpoints"
msgstr "Puncte de vedere"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "Vizualizări"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr "Vizibil în toate paginile blogurilor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "Vizibil pe site-ul curent"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "WRITE HERE OR DRAG BUILDING BLOCKS"
msgstr "SCRIEȚI AICI SAU TRAGEȚI BLOCURILE DE CONSTRUCȚIE"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "Pagină web"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "Bloguri de site-uri web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "Mesaje Website"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtru fragment de site"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "URL website"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "Istoric comunicare website"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "Website meta descriere"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website meta cuvinte cheie"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "Metatitlu website"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imagine grafică deschisă a site-ului web"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr "Ce se întâmplă dacă vă lasă să rulați Hubble"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""
"În timp ce oricine poate ridica ochii și se poate îndrăgosti de stele în "
"orice moment, distracția astronomiei este să înveți cum să devii din ce în "
"ce mai priceput și mai echipat în privirea stelelor pe care le vezi și "
"înțelegi tot mai mult de fiecare dată când ridici privirea. Iată câțiva pași"
" pe care îi puteți face pentru ca momentele pe care le puteți dedica hobby-"
"ului dvs. de astronomie să fie mult mai plăcute."

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "With a View"
msgstr "Cu vedere"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr "Scrie un mic text aici pentru a descrie blogul sau compania ta."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Write a title, the subtitle is optional."
msgstr "Scrieți un titlu, subtitlul este opțional."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""
"Ar trebui să luați întotdeauna în considerare cu atenție tipul de facilități"
" de care aveți nevoie în dormitor și să găsiți hotelul care are cele pe care"
" le considerați importante. Site-ul directorului hotelier ar trebui să "
"detalieze aspecte precum: dimensiunea patului, acces la Internet (costul "
"acestuia, indiferent dacă există conexiune WIFI sau conexiune în bandă largă"
" prin cablu), facilități gratuite, vedere din cameră și oferte de lux, cum "
"ar fi un meniu de perne sau un meniu de baie, alegerea camere pentru "
"fumători sau nefumători etc."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""
"Veți vedea toată frumusețea pe care Maui o poate oferi și se poate distra de"
" minune pentru întreaga familie. Tururile nu sunt prea scumpe și durează de "
"la patruzeci și cinci de minute la peste o oră. Puteți vedea locuri care "
"sunt de obicei inaccesibile cu tururile cu elicopterul Maui. Locurile care "
"nu sunt disponibile pe jos sau cu vehiculul pot fi văzute pe calea aerului. "
"Atracții uluitoare îi așteaptă pe cei care sunt pregătiți să facă niște "
"excursii distractive cu elicopterul Maui. Dacă veți sta pe insulă pentru o "
"perioadă considerabilă de timp, vă recomandăm să faceți mai multe tururi cu "
"elicopterul Maui."

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr "aventură"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr "blog. Faceți clic aici pentru a accesa blogul:"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr "breadcrumb"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "de"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr "descoperire"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr "Ghizi"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "a fost publicat pe"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr "hotele"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "în"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "in <i class=\"fa fa-folder-open text-muted\"/>"
msgstr "în <i class=\"fa fa-folder-open text-muted\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "in <i class=\"fa fa-folder-open text-white-75\"/>"
msgstr "în <i class=\"fa fa-folder-open text-white-75\"/>"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr "telescoape"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr "pentru a lăsa un comentariu"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "nepublicat"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted mr-1\"/>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr "| Niciun comentariu încă"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "⌙ Hover effect"
msgstr ""
