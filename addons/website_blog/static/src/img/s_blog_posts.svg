<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="82" height="55" x="0" y="0"/>
    <linearGradient id="linearGradient-3" x1="72.875%" x2="40.332%" y1="46.753%" y2="35.353%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-4" x1="88.517%" x2="50%" y1="41.799%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-5" width="22" height="3" x="14" y="21"/>
    <filter id="filter-6" width="104.5%" height="166.7%" x="-2.3%" y="-16.7%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <path id="path-7" d="M24 31v1H14v-1h10zm9-3v1H14v-1h19z"/>
    <filter id="filter-8" width="105.3%" height="150%" x="-2.6%" y="-12.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.2 0"/>
    </filter>
    <path id="path-9" d="M59 32.5a2.41 2.41 0 0 1-.73 1.77 2.41 2.41 0 0 1-1.77.73 2.41 2.41 0 0 1-1.77-.73A2.41 2.41 0 0 1 54 32.5c0-.694.243-1.285.73-1.77A2.41 2.41 0 0 1 56.5 30a2.41 2.41 0 0 1 1.77.73A2.41 2.41 0 0 1 59 32.5zm5.998 1.653a.743.743 0 0 1-.209.59.723.723 0 0 1-.577.257h-1.657a.754.754 0 0 1-.528-.203.747.747 0 0 1-.245-.51c-.18-1.873-.935-3.475-2.265-4.805-1.33-1.33-2.931-2.085-4.805-2.265a.747.747 0 0 1-.51-.246.754.754 0 0 1-.202-.528v-1.657c0-.238.086-.43.258-.577a.715.715 0 0 1 .528-.209h.06c1.31.106 2.562.436 3.757.988a10.853 10.853 0 0 1 3.179 2.229 10.856 10.856 0 0 1 2.228 3.18 11.01 11.01 0 0 1 .988 3.756zm6 .038a.698.698 0 0 1-.217.568.714.714 0 0 1-.556.241H68.5a.754.754 0 0 1-.537-.211.722.722 0 0 1-.236-.513 13.5 13.5 0 0 0-1.22-4.933c-.715-1.557-1.647-2.91-2.794-4.056-1.147-1.147-2.499-2.08-4.056-2.796a13.672 13.672 0 0 0-4.932-1.231.722.722 0 0 1-.513-.235.74.74 0 0 1-.211-.526v-1.726c0-.226.08-.41.241-.556a.723.723 0 0 1 .532-.217h.036a16.75 16.75 0 0 1 6.054 1.449A16.924 16.924 0 0 1 66 22.999a16.926 16.926 0 0 1 3.55 5.137 16.755 16.755 0 0 1 1.448 6.055z"/>
    <filter id="filter-11" width="105.9%" height="111.8%" x="-2.9%" y="-2.9%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_latest_posts">
      <rect width="82" height="60" class="bg"/>
      <g class="group_2">
        <g class="group" opacity=".5">
          <g class="oval___oval_mask">
            <mask id="mask-2" fill="#fff">
              <use xlink:href="#path-1"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
            <circle cx="65.5" cy="11.5" r="7.5" fill="#F3EC60" class="oval" mask="url(#mask-2)"/>
            <ellipse cx="63" cy="55.5" fill="url(#linearGradient-3)" class="oval" mask="url(#mask-2)" rx="28" ry="16.5"/>
            <ellipse cx="6.5" cy="52.5" fill="url(#linearGradient-4)" class="oval" mask="url(#mask-2)" rx="42.5" ry="22.5"/>
          </g>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
          <use fill="#FFF" fill-opacity=".95" xlink:href="#path-5"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-8)" xlink:href="#path-7"/>
          <use fill="#FFF" fill-opacity=".8" xlink:href="#path-7"/>
        </g>
        <mask id="mask-10" fill="#fff">
          <use xlink:href="#path-9"/>
        </mask>
        <g class="rss">
          <use fill="#000" filter="url(#filter-11)" xlink:href="#path-9"/>
          <use fill="#FFF" fill-opacity=".95" xlink:href="#path-9"/>
        </g>
      </g>
    </g>
  </g>
</svg>
