<?xml version="1.0"?>
<odoo>

        <!-- Blog views -->
        <record id="view_blog_blog_list" model="ir.ui.view">
            <field name="name">blog.blog.list</field>
            <field name="model">blog.blog</field>
            <field name="arch" type="xml">
                <tree string="Blogs">
                    <field name="name"/>
                    <field name="blog_post_count"/>
                    <field name="website_id" groups="website.group_multi_website"/>
                    <field name="active" invisible="1"/>
                </tree>
            </field>
        </record>
        <record id="view_blog_blog_form" model="ir.ui.view">
            <field name="name">blog.blog.form</field>
            <field name="model">blog.blog</field>
            <field name="arch" type="xml">
                <form string="Blog">
                    <sheet>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="name"/>
                            <field name="subtitle"/>
                            <field name="website_id" options="{'no_create': True}" groups="website.group_multi_website"/>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- page list view  -->
        <record id="view_blog_post_list" model="ir.ui.view">
            <field name="name">blog.post.list</field>
            <field name="model">blog.post</field>
            <field name="arch" type="xml">
                <tree string="Blog Posts" multi_edit="1" sample="1">
                    <field name="name"/>
                    <field name="active" invisible="1"/>
                    <field name="author_id"/>
                    <field name="blog_id"/>
                    <field name="website_id" groups="website.group_multi_website"/>
                    <field name="is_published" string="Is Published" optional="hide"/>
                    <field name="visits" readonly="1"/>
                    <field name="create_uid" invisible="1"/>
                    <field name="write_uid"/>
                    <field name="write_date"/>
                </tree>
            </field>
        </record>
        <!-- page form view  -->
        <record id="view_blog_post_form" model="ir.ui.view">
            <field name="name">blog.post.form</field>
            <field name="model">blog.post</field>
            <field name="arch" type="xml">
                <form string="Blog Post">
                    <sheet>
                        <div class="oe_button_box" name="button_box" attrs="{'invisible': [('active', '=', False)]}">
                            <field name="is_published" widget="website_redirect_button"/>
                        </div>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                        <group name="blog_details">
                            <field name="blog_id"/>
                            <field name="active" invisible="1"/>
                            <field name="name" placeholder="Blog Post Title"/>
                            <field name="subtitle" placeholder="Blog Subtitle"/>
                            <field name="tag_ids" widget="many2many_tags"/>
                            <field name="website_id" groups="website.group_multi_website"/>
                        </group>
                        <group name="publishing_details" string="Publishing Options">
                            <field name="author_id"/>
                            <field name="create_date" groups="base.group_no_one"/>
                            <field name="post_date"/>
                            <field name="write_uid"/>
                            <field name="write_date"/>
                        </group>
                        <notebook>
                            <page name="seo" string="SEO" groups="base.group_no_one">
                                <group name="default_opengraph">
                                    <field name="website_meta_title" string="Meta Title"/>
                                    <field name="website_meta_description" string="Meta Description"/>
                                    <field name="website_meta_keywords" string="Meta Keywords" help="Separate every keyword with a comma"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        <!-- page search view  -->
        <record id="view_blog_post_search" model="ir.ui.view">
            <field name="name">blog.post.search</field>
            <field name="model">blog.post</field>
            <field name="arch" type="xml">
                <search string="Blog Post">
                    <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
                    <field name="name" string="Content" filter_domain="['|', ('name','ilike',self), ('content','ilike',self)]"/>
                    <field name="write_uid"/>
                    <field name="blog_id"/>
                    <group expand="0" string="Group By">
                        <filter string="Blog" name="group_by_blog" domain="[]" context="{'group_by': 'blog_id'}"/>
                        <filter string="Author" name="group_by_author" domain="[]" context="{'group_by': 'create_uid'}"/>
                        <filter string="Last Contributor" name="last_contributor" domain="[]" context="{'group_by': 'write_uid'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="blog_post_view_kanban" model="ir.ui.view">
            <field name="name">blog.post.kanban</field>
            <field name="model">blog.post</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" sample="1">
                    <field name="name"/>
                    <field name="blog_id"/>
                    <field name="author_id"/>
                    <field name="post_date"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click">
                                <div class="row mb4">
                                    <strong class="col-8">
                                        <span t-esc="record.name.value"/>
                                    </strong>
                                    <strong class="col-4 text-right">
                                        <span t-esc="record.blog_id.value"/>
                                    </strong>
                                    <div class="col-8">
                                        <i class="fa fa-clock-o" role="img" aria-label="Post date" title="Post date"/><span t-esc="record.post_date.value"/>
                                    </div>
                                    <div class="col-4 text-right">
                                        <img t-if="record.author_id.raw_value"
                                             t-att-title="record.author_id.value"
                                             t-att-alt="record.author_id.value"
                                             class="oe_kanban_avatar o_image_24_cover"
                                             t-att-src="kanban_image('res.partner', 'avatar_128', record.author_id.raw_value)"/>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- page action -->
        <record id="action_blog_post" model="ir.actions.act_window">
            <field name="name">Blog Posts</field>
            <field name="res_model">blog.post</field>
            <field name="view_mode">tree,form,kanban</field>
            <field name="view_id" ref="view_blog_post_list"/>
            <field name="search_view_id" ref="view_blog_post_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new blog post
                </p>
            </field>
        </record>

        <record id="blog_blog_view_search" model="ir.ui.view">
            <field name="name">blog.blog.search</field>
            <field name="model">blog.blog</field>
            <field name="arch" type="xml">
                <search string="Blog">
                    <field name="name"/>
                    <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
                </search>
            </field>
        </record>

        <record id="action_blog_blog" model="ir.actions.act_window">
            <field name="name">Blogs</field>
            <field name="res_model">blog.blog</field>
            <field name="view_mode">tree,form</field>
        </record>

        <record id="blog_tag_tree" model="ir.ui.view">
            <field name="name">blog_tag_tree</field>
            <field name="model">blog.tag</field>
            <field name="arch" type="xml">
                <tree string="Tag List">
                    <field name="name"/>
                    <field name="category_id"/>
                    <field name="post_ids"/>
                </tree>
            </field>
        </record>

        <record id="blog_tag_form" model="ir.ui.view">
            <field name="name">blog_tag_form</field>
            <field name="model">blog.tag</field>
            <field name="arch" type="xml">
                <form string="Tag Form">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="category_id"/>
                        </group>
                        <label for="post_ids" string="Used in: "/>
                        <field name="post_ids"/>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="action_tags" model="ir.actions.act_window">
            <field name="name">Blog Tags</field>
            <field name="res_model">blog.tag</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="blog_tag_tree"/>
        </record>

        <record id="blog_tag_category_form" model="ir.ui.view">
            <field name="name">blog_tag_category_form</field>
            <field name="model">blog.tag.category</field>
            <field name="arch" type="xml">
                <form string="Tag Category Form">
                    <sheet>
                        <group>
                            <field name="name"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="blog_tag_category_tree" model="ir.ui.view">
            <field name="name">blog_tag_category_tree</field>
            <field name="model">blog.tag.category</field>
            <field name="arch" type="xml">
                <tree string="Tag Categories">
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="action_tag_category" model="ir.actions.act_window">
            <field name="name">Tag Category</field>
            <field name="res_model">blog.tag.category</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="blog_tag_category_tree"/>
        </record>

        <menuitem name="Blogs"
            id="menu_website_blog_root"
            sequence="20"
            parent="website.menu_website_configuration"
            groups="website.group_website_designer"
            action="action_blog_post"/>

        <menuitem name="Blogs"
            id="menu_website_blog_root_global"
            sequence="100"
            parent="website.menu_website_global_configuration"
            groups="website.group_website_designer"/>

        <menuitem id="menu_website_blog_tag_category_global" parent="menu_website_blog_root_global"
                  name="Tag Categories" action="action_tag_category" sequence="50" />

        <menuitem id="menu_blog_tag_global" parent="menu_website_blog_root_global" name="Tags" action="action_tags" sequence="40" />

        <menuitem id="menu_blog_global" parent="menu_website_blog_root_global" name="Blogs" action="action_blog_blog" sequence="20"/>
</odoo>
