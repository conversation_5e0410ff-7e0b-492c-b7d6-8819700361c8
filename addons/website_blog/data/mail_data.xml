<?xml version="1.0" encoding="UTF-8"?>
<odoo><data noupdate="1">

    <!-- Blog-related subtypes for messaging / Chatter -->
    <record id="mt_blog_blog_published" model="mail.message.subtype">
        <field name="name">Published Post</field>
        <field name="res_model">blog.blog</field>
        <field name="default" eval="True"/>
        <field name="description">Published Post</field>
    </record>

</data></odoo>
