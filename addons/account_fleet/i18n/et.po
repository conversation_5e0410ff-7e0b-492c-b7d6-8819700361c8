# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_fleet
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> Õigus <<EMAIL>>, 2022
# Anna, 2023
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-14 15:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__account_move_ids
msgid "Account Move"
msgstr "Laoliikumine"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "Bills"
msgstr "Ostuarved"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__bill_count
msgid "Bills Count"
msgstr "Ostuarvete arv"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_automatic_entry_wizard
msgid "Create Automatic Entries"
msgstr "Loo automaatsed kanded"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.account_move_view_tree
msgid "Creation Date"
msgstr "Loomise kuupäev"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move
msgid "Journal Entry"
msgstr "Andmiku kanne"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move_line
msgid "Journal Item"
msgstr "Andmiku kanderida"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__need_vehicle
msgid "Need Vehicle"
msgstr "Vajalik sõiduk"

#. module: account_fleet
#: code:addons/account_fleet/models/account_move.py:0
#, python-format
msgid ""
"Service Vendor Bill: <a href=# data-oe-model=account.move data-oe-"
"id={move_id}>{move_name}</a>"
msgstr ""

#. module: account_fleet
#: model:ir.model.fields,help:account_fleet.field_account_move_line__need_vehicle
msgid "Technical field to decide whether the vehicle_id field is editable"
msgstr ""

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__vehicle_id
msgid "Vehicle"
msgstr "Sõiduk"

#. module: account_fleet
#: model:fleet.service.type,name:account_fleet.data_fleet_service_type_vendor_bill
msgid "Vendor Bill"
msgstr "Tarnija arve"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "show the vendor bills for this vehicle"
msgstr "näita selle sõiduki ostuarveid"
