# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_fleet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON>, 2023
# Wil <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-14 15:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__account_move_ids
msgid "Account Move"
msgstr "Bút to<PERSON> to<PERSON>"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "Bills"
msgstr "Hoá đơn"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__bill_count
msgid "Bills Count"
msgstr "Bills Count"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_automatic_entry_wizard
msgid "Create Automatic Entries"
msgstr "Tạo bút toán tự động"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.account_move_view_tree
msgid "Creation Date"
msgstr "Ngày tạo"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move
msgid "Journal Entry"
msgstr "Bút toán sổ nhật ký"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move_line
msgid "Journal Item"
msgstr "Bút toán"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__need_vehicle
msgid "Need Vehicle"
msgstr "Need Vehicle"

#. module: account_fleet
#: code:addons/account_fleet/models/account_move.py:0
#, python-format
msgid ""
"Service Vendor Bill: <a href=# data-oe-model=account.move data-oe-"
"id={move_id}>{move_name}</a>"
msgstr ""
"Service Vendor Bill: <a href=# data-oe-model=account.move data-oe-"
"id={move_id}>{move_name}</a>"

#. module: account_fleet
#: model:ir.model.fields,help:account_fleet.field_account_move_line__need_vehicle
msgid "Technical field to decide whether the vehicle_id field is editable"
msgstr "Technical field to decide whether the vehicle_id field is editable"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__vehicle_id
msgid "Vehicle"
msgstr "Phương tiện"

#. module: account_fleet
#: model:fleet.service.type,name:account_fleet.data_fleet_service_type_vendor_bill
msgid "Vendor Bill"
msgstr "Hóa đơn Nhà cung cấp"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "show the vendor bills for this vehicle"
msgstr "show the vendor bills for this vehicle"
