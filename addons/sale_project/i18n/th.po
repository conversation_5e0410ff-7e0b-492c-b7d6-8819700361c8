# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-14 15:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Create Invoice"
msgstr "สร้างใบแจ้งหนี้"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "สร้างคำสั่ง"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr "แสดงโปรเจกต์"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr "สร้างโปรเจกต์"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr "สร้างงาน"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr "มี SO เพื่อออกใบแจ้งหนี้"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice as soon as this service is sold, and create a task in an existing "
"project to track the time spent."
msgstr ""
"ออกใบแจ้งหนี้ทันทีที่ขายบริการนี้ "
"และสร้างงานในโปรเจกต์ที่มีอยู่เพื่อติดตามเวลาที่ใช้ไป"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold, and create a "
"project for the order with a task for each sales order line to track the "
"time spent."
msgstr ""
"ออกใบแจ้งหนี้ตามจำนวนที่สั่งทันทีที่มีการขายบริการนี้ "
"และสร้างโปรเจกต์สำหรับคำสั่งที่มีการระบุงานสำหรับแต่ล่ะไลน์คำสั่งขายเพื่อติดตามเวลาที่ใช้"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold, and create an "
"empty project for the order to track the time spent."
msgstr ""
"ออกใบแจ้งหนี้ตามจำนวนที่สั่งทันทีที่มีการขายบริการนี้ "
"และสร้างโปรเจกต์ที่ว่างเปล่าสำหรับคำสั่งเพื่อติดตามเวลาที่ใช้ไป"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr "ออกใบแจ้งหนี้ตามจำนวนที่สั่งทันทีที่มีการขายบริการนี้"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__is_service
msgid "Is a Service"
msgstr "เป็นบริการ"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid "New"
msgstr "ใหม่"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "None"
msgstr "ไม่มี"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "ไม่มี"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr "จำนวนโปรเจกต์"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__invoice_count
msgid "Number of invoices"
msgstr "จำนวนใบแจ้งหนี้"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""
"ในการยืนยันคำสั่งขาย สินค้านี้สามารถสร้างโปรเจกต์และ/หรืองานได้ จากนั้นคุณสามารถติดตามบริการที่คุณขายได้\n"
"        'ในโปรเจกต์ของคำสั่งขาย': จะใช้โปรเจกต์ที่กำหนดค่าของคำสั่งขายหากมีการกำหนดไว้หรือทางเลือกอื่นในการสร้างโปรเจกต์ใหม่อิงตามเทมเพลตที่เลือก"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product"
msgstr "สินค้า"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "โปรเจกต์"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "โปรเจกต์และงาน"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "เทมเพลตโปรเจกต์"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__project_id
msgid "Project generated by the sales order item"
msgstr "โปรเจกต์ที่สร้างโดยรายการคำสั่งขาย"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "คำสั่งขายโปรเจกต์"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Projects"
msgstr "โปรเจกต์"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "โปรเจกต์ที่ใช้ในคำสั่งขายนี้"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
msgid "Sale Order"
msgstr "คำสั่งขาย"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order"
msgstr "คำสั่งขาย"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
msgid "Sales Order Id"
msgstr "ไอดีคำสั่งขาย"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order Item"
msgstr "รายการคำสั่งขาย"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added, in "
"order to be invoiced to your customer."
msgstr ""
"รายการคำสั่งขายที่จะเพิ่มเวลาที่ใช้ในงานนี้เพื่อออกใบแจ้งหนี้ให้กับลูกค้าของคุณ"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "ไลน์คำสั่งขาย"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__is_service
msgid ""
"Sales Order item should generate a task and/or a project, depending on the "
"product settings."
msgstr ""
"รายการคำสั่งขายควรสร้างงานและ/หรือโปรเจกต์ ขึ้นอยู่กับการตั้งค่าสินค้า"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item to which the project is linked. Link the timesheet entry to"
" the sales order item defined on the project. Only applies on tasks without "
"sale order item defined, and if the employee is not in the 'Employee/Sales "
"Order Item Mapping' of the project."
msgstr ""
"รายการใบสั่งขายที่เชื่อมโยงกับโปรเจกต์ "
"เชื่อมโยงใบบันทึกเวลากับรายการคำสั่งขายที่ระบุไว้ในโปรเจกต์ "
"ใช้เฉพาะกับงานที่ไม่มีการระบุรายการคำสั่งขาย และถ้าพนักงานไม่อยู่ใน "
"'แผนผังรายการคำสั่งพนักงานและการขาย' ของโปรเจกต์"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "คำสั่งขายที่เชื่อมโยงโปรเจกต์"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "คำสั่งขายที่มีการเชื่อมโยงงาน"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "ค้นหาในใบแจ้งหนี้"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "ค้นหาในคำสั่งขาย"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order Item"
msgstr "ค้นหาในรายการคำสั่งขาย"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__project_id
#: model:ir.model.fields,help:sale_project.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""
"เลือกโปรเจกต์ที่เรียกเก็บเงินได้ซึ่งสามารถสร้างงานได้ "
"จำเป็นต้องตั้งค่านี้สำหรับแต่ละบริษัท"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,help:sale_project.field_product_template__project_template_id
msgid ""
"Select a billable project to be the skeleton of the new created project when"
" selling the current product. Its stages and tasks will be duplicated."
msgstr ""
"เลือกโปรเจกต์ที่เรียกเก็บเงินได้เพื่อเป็นโครงร่างของโปรเจกต์ที่สร้างขึ้นใหม่เมื่อขายสินค้าปัจจุบัน"
" ขั้นตอนและงานจะถูกทำซ้ำ"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr "เลือกโปรเจกต์ที่เรียกเก็บเงินไม่ได้แต่สามารถสร้างงานได้"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "งาน"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"Task Created (%s): <a href=# data-oe-model=project.task data-oe-id=%d>%s</a>"
msgstr ""
"งานที่ถูกสร้าง (%s): <a href=# data-oe-model=project.task data-oe-"
"id=%d>%s</a>"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "การเกิดซ้ำของงาน"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__task_id
msgid "Task generated by the sales order item"
msgstr "งานที่สร้างโดยรายการคำสั่งขาย"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "งาน"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "วิเคราะห์งาน"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr "งานที่เกี่ยวข้องกับการขายนี้"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr "สินค้า %sไม่ควรมีโปรเจกต์ทั่วโลกเพราะจะสร้างโปรเจกต์"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""
"สินค้า  %sไม่ควรมีโปรเจกต์หรือเทมเพลตโปรเจกต์เนื่องจากจะไม่สร้างโปรเจกต์"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr "สินค้า %s ไม่ควรมีเทมเพลตโปรเจกต์เนื่องจากจะสร้างงานในโปรเจกต์ทั่วโลก"

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "There is nothing to invoice in this project."
msgstr "ไม่มีรายการที่จะออกใบแจ้งหนี้ในโปรเจกต์นี้"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"This task has been created from: <a href=# data-oe-model=sale.order data-oe-"
"id=%d>%s</a> (%s)"
msgstr ""
"งานนี้ถูกสร้างขึ้นจาก:<a href=# data-oe-model=sale.order data-oe-"
"id=%d>%s</a> (%s)"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "ออกใบแจ้งหนี้"

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""
"คุณไม่สามารถเชื่อมต่อรายการคำสั่ง%(order_id)s - %(product_id)s "
"กับงานนี้เนื่องจากเป็นค่าใช้จ่ายที่ออกใบแจ้งหนี้ใหม่"

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You have to unlink the task from the sale order item in order to delete it."
msgstr "คุณต้องยกเลิกการเชื่อมโยงงานจากรายการคำสั่งขายเพื่อที่จะลบออก"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "for sale order item:"
msgstr "สำหรับรายการคำสั่งขาย:"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "for sale order:"
msgstr "สำหรับคำสั่งขาย:"
