# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_ingenico
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_ingenico
#: code:addons/payment_ingenico/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_ingenico
#: code:addons/payment_ingenico/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_userid
msgid "API User ID"
msgstr ""

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_password
msgid "API User Password"
msgstr ""

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_alias_usage
msgid "Alias Usage"
msgstr ""

#. module: payment_ingenico
#: model_terms:ir.ui.view,arch_db:payment_ingenico.ogone_s2s_form
msgid "CVC"
msgstr ""

#. module: payment_ingenico
#: model_terms:ir.ui.view,arch_db:payment_ingenico.ogone_s2s_form
msgid "Card number"
msgstr ""

#. module: payment_ingenico
#: model_terms:ir.ui.view,arch_db:payment_ingenico.ogone_s2s_form
msgid "Cardholder name"
msgstr ""

#. module: payment_ingenico
#: model_terms:ir.ui.view,arch_db:payment_ingenico.ogone_s2s_form
msgid "Expires (MM / YY)"
msgstr ""

#. module: payment_ingenico
#: model:ir.model.fields,help:payment_ingenico.field_payment_acquirer__ogone_alias_usage
msgid ""
"If you want to use Ogone Aliases, this default Alias Usage will be presented"
" to the customer as the reason you want to keep his payment data"
msgstr ""

#. module: payment_ingenico
#: model:ir.model.fields.selection,name:payment_ingenico.selection__payment_acquirer__provider__ogone
msgid "Ingenico"
msgstr ""

#. module: payment_ingenico
#: code:addons/payment_ingenico/models/payment.py:0
#, python-format
msgid "Ogone: invalid shasign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_ingenico
#: code:addons/payment_ingenico/models/payment.py:0
#, python-format
msgid "Ogone: received data for reference %s"
msgstr ""

#. module: payment_ingenico
#: code:addons/payment_ingenico/models/payment.py:0
#, python-format
msgid ""
"Ogone: received data with missing reference (%s) or pay_id (%s) or shasign "
"(%s)"
msgstr ""

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_pspid
msgid "PSPID"
msgstr ""

#. module: payment_ingenico
#: model:ir.model,name:payment_ingenico.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_ingenico
#: model:ir.model,name:payment_ingenico.model_payment_token
msgid "Payment Token"
msgstr ""

#. module: payment_ingenico
#: model:ir.model,name:payment_ingenico.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__provider
msgid "Provider"
msgstr ""

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_shakey_in
msgid "SHA Key IN"
msgstr ""

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_shakey_out
msgid "SHA Key OUT"
msgstr ""
