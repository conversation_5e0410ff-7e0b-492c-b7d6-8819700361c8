<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_de_chart_template" model="account.chart.template">
        <field name="code_digits">4</field>
        <field name="property_account_receivable_id" ref="l10n_de_skr03.account_1410"/>
        <field name="property_account_payable_id" ref="l10n_de_skr03.account_1610"/>
        <field name="property_account_expense_categ_id" ref="l10n_de_skr03.account_3400"/>
        <field name="property_account_income_categ_id" ref="l10n_de_skr03.account_8400"/>
        <field name="property_stock_account_input_categ_id" ref="l10n_de_skr03.account_3970"/>
        <field name="property_stock_account_output_categ_id" ref="l10n_de_skr03.account_3980"/>
        <field name="property_stock_valuation_account_id" ref="l10n_de_skr03.account_3960"/>
        <field name="property_tax_payable_account_id" ref="l10n_de_skr03.account_1797"/>
        <field name="property_tax_receivable_account_id" ref="l10n_de_skr03.account_1545"/>
        <field name="property_advance_tax_payment_account_id" ref="l10n_de_skr03.account_1780"/>
        <field name="income_currency_exchange_account_id" ref="l10n_de_skr03.account_2660"/>
        <field name="expense_currency_exchange_account_id" ref="l10n_de_skr03.account_2150"/>
        <field name="default_pos_receivable_account_id" ref="l10n_de_skr03.account_1411" />
    </record>
</odoo>
